<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Snap App</title>
  <!-- Global styles remain as external CSS -->
  <link rel="stylesheet" href="snapapp.css">
  
  <!-- Chart System Styles -->
  <link rel="stylesheet" href="components/charts/snap-charts.css">
</head>
<body>
  <!-- The entire app structure will be created by JavaScript -->
  
  <!-- Logger (load first to control console output) -->
  <script src="utils/logger.js"></script>
  <!-- Mock Zero Data toggle and helpers (set USE_MOCK_ZERO_DATA here) -->
  <script src="utils/mock-zero-data.js"></script>

  <!-- Performance Optimization Scripts -->
  <link rel="stylesheet" href="performance-optimizations/lazy-loading-styles.css">
  <script src="performance-optimizations/event-cleanup-manager.js"></script>
  <script src="performance-optimizations/data-cache-manager.js"></script>
  <script src="performance-optimizations/dom-optimizer.js"></script>
  <script src="performance-optimizations/viewport-lazy-loader.js"></script>
  <script src="performance-optimizations/indexeddb-manager.js"></script>
  <script src="performance-optimizations/realtime-data-manager.js"></script>
  <script src="performance-optimizations/memory-monitor.js"></script>
  <script src="performance-optimizations/daily-sales-manager.js"></script>
  <script src="performance-optimizations/products-page-manager.js"></script>

  <!-- Timezone Utility Script -->
  <script src="utils/timezone.js"></script>

  <!-- Date Change Manager Script -->
  <script src="utils/date-change-manager.js"></script>

  <!-- Chart System Script -->
  <script src="components/charts/snap-charts.js"></script>
  <!-- External mock listings for dashboard (today/yesterday) -->
  <script src="components/dashboard/mock-listings.js"></script>
  <!-- External mock cards (monthly, top four, lifetime) -->
  <script src="components/dashboard/mock-cards.js"></script>
  <!-- Central dashboard static/mock data overrides -->
  <script src="components/dashboard/mock-dashboard-data.js"></script>

  <!-- Main application script that contains embedded HTML -->
  <script src="snapapp.js"></script>
</body>
</html> 