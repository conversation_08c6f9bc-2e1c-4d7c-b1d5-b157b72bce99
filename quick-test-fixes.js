/**
 * Quick Test for Debug Fixes
 * Run this in the browser console after the dashboard is loaded
 */

console.log('🔧 Quick Debug Fixes Test');

// Check if components are available
const components = {
    'RealTimeDataManager': window.RealTimeDataManager,
    'EventCleanupManager': window.EventCleanupManager,
    'SnapChart': window.SnapChart,
    'SnapLogger': window.SnapLogger,
    'createDebouncedFunction': window.createDebouncedFunction,
    'extractUnitsValue': window.extractUnitsValue,
    'createInsightsModal': window.createInsightsModal,
    'createSafeElementFromTrustedHTML': window.createSafeElementFromTrustedHTML
};

console.log('📋 Component Availability:');
Object.entries(components).forEach(([name, component]) => {
    const status = component ? '✅' : '❌';
    console.log(`${status} ${name}: ${component ? 'Available' : 'Missing'}`);
});

// Quick functional tests
console.log('\n🧪 Quick Functional Tests:');

// Test 1: Timer Cleanup
if (window.EventCleanupManager) {
    try {
        const timeoutId = window.EventCleanupManager.setTimeout(() => {}, 1000);
        window.EventCleanupManager.clearTimeout(timeoutId);
        console.log('✅ Timer cleanup: Working');
    } catch (e) {
        console.log('❌ Timer cleanup: Failed -', e.message);
    }
} else {
    console.log('❌ Timer cleanup: EventCleanupManager not available');
}

// Test 2: Logger System
if (window.SnapLogger) {
    try {
        const originalLevel = window.SnapLogger.level;
        window.SnapLogger.setLevel('warn');
        const testPassed = window.SnapLogger.level === 'warn';
        window.SnapLogger.setLevel(originalLevel);
        console.log(testPassed ? '✅ Logger system: Working' : '❌ Logger system: Failed');
    } catch (e) {
        console.log('❌ Logger system: Failed -', e.message);
    }
} else {
    console.log('❌ Logger system: SnapLogger not available');
}

// Test 3: Real-time Key Mapping
if (window.RealTimeDataManager && window.handleRealTimeDataUpdate) {
    try {
        const originalHandler = window.handleRealTimeDataUpdate;
        let capturedData = null;
        
        window.handleRealTimeDataUpdate = function(data) {
            capturedData = data;
        };
        
        window.RealTimeDataManager.updateUI('salesData', [{test: 'data'}]);
        
        const testPassed = capturedData && capturedData.analytics;
        window.handleRealTimeDataUpdate = originalHandler;
        
        console.log(testPassed ? '✅ Real-time key mapping: Working' : '❌ Real-time key mapping: Failed');
    } catch (e) {
        console.log('❌ Real-time key mapping: Failed -', e.message);
    }
} else {
    console.log('❌ Real-time key mapping: Components not available');
}

// Test 4: Units Parser
if (window.extractUnitsValue) {
    try {
        // Create mock DOM
        const mockListing = document.createElement('div');
        const unitsSpan = document.createElement('span');
        const unitsBadge = document.createElement('div');
        unitsBadge.className = 'order-units-badge';
        unitsBadge.appendChild(unitsSpan);
        mockListing.appendChild(unitsBadge);
        
        unitsSpan.textContent = '+1,234';
        const result = window.extractUnitsValue(mockListing);
        
        console.log(result === 1234 ? '✅ Units parser: Working' : `❌ Units parser: Failed (got ${result})`);
    } catch (e) {
        console.log('❌ Units parser: Failed -', e.message);
    }
} else {
    console.log('❌ Units parser: extractUnitsValue not available');
}

// Test 5: XSS Protection
if (window.createSafeElementFromTrustedHTML) {
    try {
        const safeHTML = '<div class="test-class">Safe content</div>';
        const element = window.createSafeElementFromTrustedHTML(safeHTML, 'test-class');
        
        const testPassed = element && element.classList.contains('test-class');
        console.log(testPassed ? '✅ XSS protection: Working' : '❌ XSS protection: Failed');
    } catch (e) {
        console.log('❌ XSS protection: Failed -', e.message);
    }
} else {
    console.log('❌ XSS protection: createSafeElementFromTrustedHTML not available');
}

// Test 6: View Insights Modal
if (window.createInsightsModal) {
    try {
        window.createInsightsModal('sales', 'Test Period');
        const modal = document.getElementById('insights-modal');
        
        if (modal) {
            console.log('✅ View Insights modal: Working');
            modal.remove(); // Clean up
        } else {
            console.log('❌ View Insights modal: Modal not created');
        }
    } catch (e) {
        console.log('❌ View Insights modal: Failed -', e.message);
    }
} else {
    console.log('❌ View Insights modal: createInsightsModal not available');
}

// Test 7: Debounced Functions
if (window.createDebouncedFunction) {
    try {
        const debouncedFn = window.createDebouncedFunction(() => {}, 100);
        const testPassed = typeof debouncedFn === 'function';
        console.log(testPassed ? '✅ Debounced functions: Working' : '❌ Debounced functions: Failed');
    } catch (e) {
        console.log('❌ Debounced functions: Failed -', e.message);
    }
} else {
    console.log('❌ Debounced functions: createDebouncedFunction not available');
}

console.log('\n📊 Quick Test Summary:');
console.log('If you see mostly ✅ marks above, the fixes are working correctly!');
console.log('If you see ❌ marks, make sure you\'re running this on the dashboard page.');
console.log('\n💡 For detailed testing, open test-debug-fixes.html');
console.log('💡 For full validation, run validate-debug-fixes.js');

// Instructions
console.log('\n📋 INSTRUCTIONS:');
console.log('1. Make sure you\'re on the main dashboard page');
console.log('2. Wait for the dashboard to fully load');
console.log('3. Run this script in the browser console');
console.log('4. Check the results above');

// Make results available
window.quickTestResults = {
    components,
    timestamp: new Date().toISOString()
};
