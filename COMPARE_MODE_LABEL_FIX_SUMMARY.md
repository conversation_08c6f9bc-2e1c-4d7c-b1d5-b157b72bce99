# Compare Mode Label Positioning Fix

## Problem Description

When compare mode was enabled in stacked column charts, the bottom axis date labels (month/year labels like "JAN '25", "FEB '25", etc.) were being positioned incorrectly. Instead of being centered under the main columns (darker blue), they were being centered under the comparison columns (lighter blue) or positioned between both columns.

## Root Cause

The issue was in the `drawGroupLabels` function in `components/charts/snap-charts.js`. When compare mode is enabled:

1. **Column Layout**: Each column group contains:
   - Comparison column (lighter blue) at position `groupStartX`
   - Gap of 16px
   - Main column (darker blue) at position `groupStartX + comparisonColumnWidth + comparisonOffset`

2. **Label Positioning Bug**: The labels were being positioned using:
   ```javascript
   labelX = groupStartX + (columnWidth / 2);
   ```
   This centered the label under the comparison column instead of the main column.

## Solution

Updated the label positioning logic in the `drawGroupLabels` function (lines 5582-5597) to properly calculate the main column center when compare mode is enabled:

### Before (Incorrect):
```javascript
if (positioningInfo.hasCompare && positioningInfo.mainColumnWidth) {
  // In compare mode: use main column width to center under main column only
  columnWidth = positioningInfo.mainColumnWidth;
} else {
  // Normal mode: use the appropriate column width
  columnWidth = positioningInfo.columnWidth || positioningInfo.columnGroupWidth || 24;
}
labelX = groupStartX + (columnWidth / 2);
```

### After (Correct):
```javascript
if (positioningInfo.hasCompare && positioningInfo.mainColumnWidth) {
  // In compare mode: center labels under main column only
  // Main column is positioned after comparison column and gap
  const comparisonColumnWidth = positioningInfo.comparisonColumnWidth || positioningInfo.mainColumnWidth;
  const comparisonOffset = positioningInfo.comparisonOffset || 16;
  const mainColumnX = groupStartX + comparisonColumnWidth + comparisonOffset;
  labelX = mainColumnX + (positioningInfo.mainColumnWidth / 2);
} else {
  // Normal mode: use the appropriate column width
  const columnWidth = positioningInfo.columnWidth || positioningInfo.columnGroupWidth || 24;
  labelX = groupStartX + (columnWidth / 2);
}
```

## Additional Changes

Updated the positioning info passed to `drawGroupLabels` in the stacked column rendering (lines 2460-2474) to include the necessary comparison column positioning data:

```javascript
const positioningInfo = {
  sidePadding,
  columnGroupWidth,
  gapBetweenGroups,
  columnAreaStartX: null,
  columnWidth: null,
  gapBetweenColumns: null,
  startX: null,
  // Add main column width for proper label alignment in compare mode
  mainColumnWidth: mainColumnWidth,
  hasCompare: hasCompare,
  // Add comparison column positioning info for proper label centering
  comparisonColumnWidth: hasCompare ? comparisonColumnWidth : null,
  comparisonOffset: hasCompare ? comparisonOffset : null
};
```

## Impact

- **Fixed**: X-axis labels are now properly centered under main columns when compare mode is enabled
- **Preserved**: Normal mode behavior remains unchanged
- **Scope**: Only affects regular stacked column charts with compare mode enabled
- **Compatibility**: No breaking changes to existing functionality

## Testing

1. **Logic Verification**: Created `verify-compare-label-fix.js` to test the mathematical calculations
2. **Visual Testing**: Created `test-compare-label-positioning.html` for interactive testing
3. **Regression Testing**: Verified normal mode still works correctly

## Files Modified

- `components/charts/snap-charts.js` (lines 2460-2474 and 5582-5597)

## Files Added

- `verify-compare-label-fix.js` - Logic verification script
- `test-compare-label-positioning.html` - Visual test page
- `COMPARE_MODE_LABEL_FIX_SUMMARY.md` - This documentation

## Verification Results

✅ Compare mode calculation: CORRECT  
✅ Normal mode compatibility: CORRECT  
✅ Overall fix status: SUCCESS

The fix ensures that when compare mode is enabled, the bottom axis date labels are perfectly centered under the main columns (darker blue) rather than under the comparison columns (lighter blue) or between both columns.
