# Dark Mode Card Background Consistency Fix

## Task Overview
Update all card components in dark mode to use the `--bg-primary` CSS variable instead of hardcoded colors for consistent theming.

## Implementation Tasks
- [x] Task 1: Update sales cards background colors to use `var(--bg-primary)`
- [x] Task 2: Update insights-and-feedback-section card background colors to use `var(--bg-primary)`
- [x] Task 3: Update payout cards and other card types to use `var(--bg-primary)`
- [x] Task 4: Verify all cards now use consistent CSS variable for background colors

## Files Modified
- `snapapp.css` - Updated card background color definitions

## Cards Updated
- **Sales cards**: Updated light theme from `#fff` to `var(--bg-primary)`, dark theme already used variable
- **Insights and feedback cards**: Updated light theme from `#fff` to `var(--bg-primary)`, dark theme already used variable  
- **Payout cards**: Already using `var(--bg-primary)` in both themes
- **Account status card**: Updated light theme from `#FFFFFF` to `var(--bg-primary)`, dark theme already used variable
- **Listings status card**: Updated light theme from `#FFFFFF` to `var(--bg-primary)`, dark theme already used variable
- **Ad spend card**: Updated light theme from `#fff` to `var(--bg-primary)`, dark theme already used variable
- **Tip card**: Already using `var(--bg-primary)` in both themes

## Result
All major card components now use the `--bg-primary` CSS variable for consistent theming across both light and dark modes. This ensures that when the theme changes, all cards automatically use the correct background color without hardcoded values.
