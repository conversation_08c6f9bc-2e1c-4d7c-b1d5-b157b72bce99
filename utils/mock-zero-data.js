(function() {
  // Global toggle: set to true to enable zero-data mock mode
  if (typeof window.USE_MOCK_ZERO_DATA === 'undefined') {
    window.USE_MOCK_ZERO_DATA = false;
  }

  const MockZeroData = {
    // Prevent repeated zeroing loops; set true after first successful apply
    appliedOnce: false,
    isEnabled() {
      return !!window.USE_MOCK_ZERO_DATA;
    },

    getZeroTodayVsPreviousYearsData() {
      // Return empty dataset; charts should handle gracefully
      return [];
    },

    getZeroMonthlySalesData(year, monthsToShow) {
      const months = Number.isFinite(monthsToShow) ? monthsToShow : 12;
      return Array.from({ length: months }, (_, i) => ({
        monthIndex: i,
        month: i + 1,
        sales: 0,
        royalties: 0,
        returns: 0,
        values: [0],
        labels: ['ALL']
      }));
    },

    getZeroLastWeekData() {
      const now = new Date();
      const data = [];
      const codes = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'];
      for (let i = 6; i >= 0; i--) {
        const d = new Date(now);
        d.setDate(now.getDate() - i);
        const marketplaces = codes.map(code => ({ code, sales: 0, royalties: 0, returns: 0 }));
        data.push({
          month: d.toLocaleString('en-US', { month: 'short' }).toUpperCase(),
          day: String(d.getDate()).padStart(2, '0'),
          year: d.getFullYear().toString().slice(-2),
          marketplaces,
          sales: 0,
          royalties: 0,
          returns: 0,
          values: marketplaces.map(() => 0),
          labels: marketplaces.map(m => m.code)
        });
      }
      return data;
    },

    applyDashboardZeroState() {
      try {
        const root = document.querySelector('.dashboard-component');
        if (!root) return;

        // Helper: force zero styling (classes) when content value is zero-ish
        const parseNumeric = (txt) => {
          if (!txt) return 0;
          const n = parseFloat(String(txt).replace(/[^0-9.-]+/g, ''));
          return Number.isFinite(n) ? n : 0;
        };
        const markAsZero = (el) => {
          if (!el) return;
          el.classList.remove('positive', 'negative', 'has-value', 'live', 'rejected', 'increase', 'decrease', 'up', 'down', 'danger', 'success');
          el.classList.add('zero');
          if (el.style) {
            el.style.color = '';
            el.style.opacity = '';
          }
        };

        // Tier value
        const tierValue = root.querySelector('.tier-value');
        if (tierValue) tierValue.textContent = '10';

        // Metric subtexts 'X of Y' => '0 of 0' (no hardcoded totals)
        root.querySelectorAll('.metric-subtext').forEach(el => {
          el.innerHTML = '0 of 0<span class="metric-remaining"></span>';
        });

        // Progress bars to 0%
        root.querySelectorAll('.progress-fill').forEach(el => { el.style.width = '0%'; });

        // Listings status numbers
        const liveNum = document.getElementById('live-listings-count');
        if (liveNum) { liveNum.textContent = '0'; markAsZero(liveNum); }
        root.querySelectorAll('.listing-status-number').forEach(el => { el.textContent = '0'; markAsZero(el); });

        // Ad spend header values and orders
        root.querySelectorAll('.ad-spend-header-value').forEach(el => {
          const t = (el.textContent || '').trim();
          const symbol = t.charAt(0);
          el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
        });
        root.querySelectorAll('.ad-spend-orders').forEach(el => { el.textContent = '(0)'; });

        // Ad spend marketplace columns
        root.querySelectorAll('.ad-spend-marketplace-col .ad-spend-value').forEach(el => {
          const t = (el.textContent || '').trim();
          const symbol = t.charAt(0);
          el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
        });
        root.querySelectorAll('.ad-spend-marketplace-col .ad-spend-acos-value').forEach(el => { el.textContent = '0.0'; });

        // Sales cards and marketplace totals
        document.querySelectorAll('.todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div').forEach(card => {
          const salesCount = card.querySelector('.sales-count');
          if (salesCount) salesCount.textContent = '0';

          // Zero inner metrics (royalties, returned, cancelled, new, ads)
          const royaltiesEl = card.querySelector('.metric-col.royalties-metric .metric-value');
          if (royaltiesEl) {
            const t = (royaltiesEl.textContent || '').trim();
            const symbol = t.charAt(0);
            royaltiesEl.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
            markAsZero(royaltiesEl);
          }
          ['returned','cancelled','new','ads'].forEach(cls => {
            const el = card.querySelector(`.metric-col.${cls}-metric .metric-value`);
            if (el) { el.textContent = '0'; markAsZero(el); }
            const per = card.querySelector(`.metric-col.${cls}-metric .metric-percentage`);
            if (per) { per.textContent = '0.0%'; markAsZero(per); per.style.display = 'none'; }
          });

          if (window.updateNoSalesStateForCard) window.updateNoSalesStateForCard(card, 0);
          if (window.updateAllPercentageCalculations) window.updateAllPercentageCalculations(card, 'all');
        });
        root.querySelectorAll('.marketplace-total-sales-count').forEach(el => { el.textContent = '0'; markAsZero(el); });
        root.querySelectorAll('.marketplace-total-earned-royalties').forEach(el => {
          const t = (el.textContent || '').trim();
          const symbol = t.charAt(0);
          el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
          markAsZero(el);
        });
        root.querySelectorAll('.marketplace-total-returned-units').forEach(el => { el.textContent = '(0)'; markAsZero(el); });

        // Four-sales-cards section (Current/Last Month/Year cards)
        document.querySelectorAll('.four-sales-cards-section .current-month-card-div, .four-sales-cards-section .last-month-card-div, .four-sales-cards-section .current-year-card-div, .four-sales-cards-section .last-year-card-div').forEach(card => {
          const salesCount = card.querySelector('.sales-count');
          if (salesCount) salesCount.textContent = '0';
          const royaltiesEl = card.querySelector('.metric-col.royalties-metric .metric-value');
          if (royaltiesEl) {
            const t = (royaltiesEl.textContent || '').trim();
            const symbol = t.charAt(0);
            royaltiesEl.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
            markAsZero(royaltiesEl);
          }
          ['returned','cancelled','new','ads'].forEach(cls => {
            const el = card.querySelector(`.metric-col.${cls}-metric .metric-value`);
            if (el) { el.textContent = '0'; markAsZero(el); }
            const per = card.querySelector(`.metric-col.${cls}-metric .metric-percentage`);
            if (per) { per.textContent = '0.0%'; markAsZero(per); per.style.display = 'none'; }
          });
          card.querySelectorAll('.marketplace-total-sales-count').forEach(el => { el.textContent = '0'; markAsZero(el); });
          card.querySelectorAll('.marketplace-total-earned-royalties').forEach(el => {
            const t = (el.textContent || '').trim();
            const symbol = t.charAt(0);
            el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
            markAsZero(el);
          });
          card.querySelectorAll('.marketplace-total-returned-units').forEach(el => { el.textContent = '(0)'; markAsZero(el); });
        });

        // Top-four-sales-cards section (Top Day/Week/Month/Year)
        document.querySelectorAll('.top-four-sales-cards-section .top-day-card-div, .top-four-sales-cards-section .top-week-card-div, .top-four-sales-cards-section .top-month-card-div, .top-four-sales-cards-section .top-year-card-div').forEach(card => {
          const salesCount = card.querySelector('.sales-count');
          if (salesCount) salesCount.textContent = '0';
          const royaltiesEl = card.querySelector('.metric-col.royalties-metric .metric-value');
          if (royaltiesEl) {
            const t = (royaltiesEl.textContent || '').trim();
            const symbol = t.charAt(0);
            royaltiesEl.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
            markAsZero(royaltiesEl);
          }
          ['returned','cancelled','new','ads'].forEach(cls => {
            const el = card.querySelector(`.metric-col.${cls}-metric .metric-value`);
            if (el) { el.textContent = '0'; markAsZero(el); }
            const per = card.querySelector(`.metric-col.${cls}-metric .metric-percentage`);
            if (per) { per.textContent = '0.0%'; markAsZero(per); per.style.display = 'none'; }
          });
          card.querySelectorAll('.marketplace-total-sales-count').forEach(el => { el.textContent = '0'; markAsZero(el); });
          card.querySelectorAll('.marketplace-total-earned-royalties').forEach(el => {
            const t = (el.textContent || '').trim();
            const symbol = t.charAt(0);
            el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
            markAsZero(el);
          });
          card.querySelectorAll('.marketplace-total-returned-units').forEach(el => { el.textContent = '(0)'; markAsZero(el); });
        });

        // Customer Reviews card zeros
        const reviewsCard = root.querySelector('.customer-reviews-card-div');
        if (reviewsCard) {
          const totalReviews = reviewsCard.querySelector('.reviews-count');
          if (totalReviews) { totalReviews.textContent = '0'; markAsZero(totalReviews); }
          // Comments and Average Rating
          const metrics = reviewsCard.querySelectorAll('.comments-ratings-div .metric-value');
          metrics.forEach((el, idx) => { el.textContent = idx === 1 ? '0.0' : '0'; markAsZero(el); });
          // Marketplace review counts
          reviewsCard.querySelectorAll('.marketplace-total-reviews-count').forEach(el => { el.textContent = '0'; markAsZero(el); });
          // Remove reviews list items
          const list = reviewsCard.querySelector('.reviews-list');
          if (list) list.innerHTML = '';
        }

        // Lifetime Insights zeros
        const lifetimeCard = root.querySelector('.lifetime-insights-card-div');
        if (lifetimeCard) {
          // Zero the main sales count in sales-analytics-div
          const salesCount = lifetimeCard.querySelector('.sales-analytics-div .sales-count');
          if (salesCount) { salesCount.textContent = '0'; markAsZero(salesCount); }
          
          // Zero all metric values in the analytics section
          lifetimeCard.querySelectorAll('.sales-analytics-div .analytics-div .metric-value').forEach(el => {
            const t = (el.textContent || '').trim();
            const symbol = t.charAt(0);
            // If looks like currency
            if (['$','£','€','¥','-'].includes(symbol)) {
              // Keep negative sign if present
              if (symbol === '-') {
                const next = t.charAt(1);
                const cur = ['$','£','€','¥'].includes(next) ? next : '$';
                el.textContent = `-${cur === '£' ? '£0.00' : cur === '€' ? '€0.00' : cur === '¥' ? '¥0' : '$0.00'}`;
              } else {
                el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
              }
            } else {
              el.textContent = '0';
            }
            markAsZero(el);
          });
          
          // Zero metric percentages in the analytics section
          lifetimeCard.querySelectorAll('.sales-analytics-div .analytics-div .metric-percentage').forEach(el => {
            el.textContent = '0.0%';
            markAsZero(el);
          });
          
          // Zero lifetime data values in the lifetime-data-div
          lifetimeCard.querySelectorAll('.lifetime-data-div .lifetime-data-item .lifetime-data-value').forEach(el => {
            const t = (el.textContent || '').trim();
            const symbol = t.charAt(0);
            // If looks like currency
            if (['$','£','€','¥','-'].includes(symbol)) {
              // Keep negative sign if present
              if (symbol === '-') {
                const next = t.charAt(1);
                const cur = ['$','£','€','¥'].includes(next) ? next : '$';
                el.textContent = `-${cur === '£' ? '£0.00' : cur === '€' ? '€0.00' : cur === '¥' ? '¥0' : '$0.00'}`;
              } else {
                el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
              }
            } else {
              el.textContent = '0';
            }
            markAsZero(el);
          });
          const taxRate = lifetimeCard.querySelector('.lifetime-tax-rate');
          if (taxRate) { taxRate.textContent = '0%'; markAsZero(taxRate); }
          
          // Zero lifetime insights marketplace totals
          lifetimeCard.querySelectorAll('.marketplace-total-sales-count').forEach(el => { el.textContent = '0'; markAsZero(el); });
          lifetimeCard.querySelectorAll('.marketplace-total-earned-royalties').forEach(el => {
            const t = (el.textContent || '').trim();
            const symbol = t.charAt(0);
            el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
            markAsZero(el);
          });
          lifetimeCard.querySelectorAll('.marketplace-total-returned-units').forEach(el => { el.textContent = '(0)'; markAsZero(el); });
        }

        // Payout Cards zeros (Next and Previous Payout)
        const payoutCards = root.querySelectorAll('.next-payout-card-div, .previous-payout-card-div');
        payoutCards.forEach(payoutCard => {
          // Show empty state and hide payout data
          const emptyState = payoutCard.querySelector('.no-payout-state');
          const emptyMarketplaceState = payoutCard.querySelector('.no-payout-marketplace');
          const marketplacesDiv = payoutCard.querySelector('.marketplaces-div');
          const payoutDataDiv = payoutCard.querySelector('.payout-data-div');
          const summaryDiv = payoutCard.querySelector('.payout-summary-div');
          const divider = payoutCard.querySelector('.sales-section-divider');
          
          if (emptyState) emptyState.style.display = 'flex';
          if (emptyMarketplaceState) emptyMarketplaceState.style.display = 'none';
          if (marketplacesDiv) marketplacesDiv.style.display = 'none';
          if (payoutDataDiv) payoutDataDiv.style.display = 'none';
          if (summaryDiv) summaryDiv.style.display = 'none';
          if (divider) divider.style.display = 'none';
          
          // Zero all payout data values (in case they're still visible)
          payoutCard.querySelectorAll('.payout-data-value').forEach(el => {
            const t = (el.textContent || '').trim();
            const symbol = t.charAt(0);
            if (['$','£','€','¥','-'].includes(symbol)) {
              // Keep negative sign if present
              if (symbol === '-') {
                const next = t.charAt(1);
                const cur = ['$','£','€','¥'].includes(next) ? next : '$';
                el.textContent = `-${cur === '£' ? '£0.00' : cur === '€' ? '€0.00' : cur === '¥' ? '¥0' : '$0.00'}`;
              } else {
                el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
              }
            } else {
              el.textContent = '0';
            }
            markAsZero(el);
          });
          
          // Zero marketplace totals in payout cards
          payoutCard.querySelectorAll('.marketplace-total-sales-count').forEach(el => { el.textContent = '0'; markAsZero(el); });
          payoutCard.querySelectorAll('.marketplace-total-earned-royalties').forEach(el => {
            const t = (el.textContent || '').trim();
            const symbol = t.charAt(0);
            el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
            markAsZero(el);
          });
          payoutCard.querySelectorAll('.marketplace-total-returned-units').forEach(el => { el.textContent = '(0)'; markAsZero(el); });
        });

        // Final normalization pass
        root.querySelectorAll('.metric-value, .metric-percentage, .marketplace-total-earned-royalties, .marketplace-total-sales-count, .marketplace-total-returned-units, .lifetime-data-value').forEach(el => {
          const txt = (el.textContent || '').trim();
          const isZeroish = /^(?:-?[\$£€¥]?0(?:\.0+)?|\(0\)|0|0\.0%|0%|¥0)$/.test(txt) || parseNumeric(txt) === 0;
          if (isZeroish) markAsZero(el);
        });
        // Mark as completed so we don't keep reapplying
        try { MockZeroData.appliedOnce = true; root.setAttribute('data-zero-applied', '1'); } catch(_) {}
      } catch (e) {
        console.warn('MockZeroData.applyDashboardZeroState failed', e);
      }
    },

    disableRealtimeIfNeeded() {
      if (!this.isEnabled()) return;
      try {
        if (window.stopRealTimeUpdates) window.stopRealTimeUpdates();
        window.startRealTimeUpdates = () => {
          if (window.SnapLogger) window.SnapLogger.info('🧪 Mock Zero Data: real-time updates disabled');
        };
      } catch (e) {
        console.warn('MockZeroData.disableRealtimeIfNeeded failed', e);
      }
    }
  };

  window.MockZeroData = MockZeroData;

  // Apply immediate runtime effects if enabled
  if (MockZeroData.isEnabled()) {
    // Inject zero-mode CSS clamp at runtime to keep main CSS clean
    try {
      const id = 'mock-zero-css-clamp';
      if (!document.getElementById(id)) {
        const style = document.createElement('style');
        style.id = id;
        style.textContent = `
          .dashboard-component[data-zero-applied="1"] .todays-sales-card-div .no-sales-state,
          .dashboard-component[data-zero-applied="1"] .yesterdays-sales-card-div .no-sales-state { display: flex !important; }
          .dashboard-component[data-zero-applied="1"] .todays-sales-card-div .sales-filter-div,
          .dashboard-component[data-zero-applied="1"] .todays-sales-card-div .search-div,
          .dashboard-component[data-zero-applied="1"] .todays-sales-card-div .search-tabs-div,
          .dashboard-component[data-zero-applied="1"] .yesterdays-sales-card-div .sales-filter-div,
          .dashboard-component[data-zero-applied="1"] .yesterdays-sales-card-div .search-div,
          .dashboard-component[data-zero-applied="1"] .yesterdays-sales-card-div .search-tabs-div { display: none !important; }
          .dashboard-component[data-zero-applied="1"] .todays-sales-card-div .listing-analytics-div,
          .dashboard-component[data-zero-applied="1"] .todays-sales-card-div .listing-section-divider,
          .dashboard-component[data-zero-applied="1"] .yesterdays-sales-card-div .listing-analytics-div,
          .dashboard-component[data-zero-applied="1"] .yesterdays-sales-card-div .listing-section-divider { display: none !important; }
        `;
        document.head.appendChild(style);
      }
    } catch (_) {}
    MockZeroData.disableRealtimeIfNeeded();
    // Hard block late initializations that would repopulate data while zero-mode is on
    try {
      window.applyFourSalesCardsMockData = async function() { return null; };
      window.applyTopFourSalesCardsMockData = async function() { return null; };
      window.refreshFourSalesCardsData = async function() { return null; };
    } catch(_) {}
  }

  // React when dashboard initializes
  window.addEventListener('dashboard:initialized', () => {
    if (MockZeroData.isEnabled()) {
      MockZeroData.applyDashboardZeroState();
      // Enforce zero immediately on marketplace focus UI: set global to 'all'
      try { window.globalMarketplaceFocus = 'all'; } catch(_) {}
      // Also re-run ALL layout restoration to ensure marketplace rows visible in ALL
      try { if (typeof window.showAllMarketplaceElements === 'function') window.showAllMarketplaceElements(); } catch(_) {}
    }
  });

  // Ensure zero state persists across async marketplace focus updates and card refreshes
  function reapplyZeroSoon() {
    if (!MockZeroData.isEnabled() || MockZeroData.appliedOnce) return;
    // Apply twice to catch staggered async DOM updates
    setTimeout(() => { try { MockZeroData.applyDashboardZeroState(); } catch (_) {} }, 10);
    setTimeout(() => { try { MockZeroData.applyDashboardZeroState(); } catch (_) {} }, 220);
  }

  // Wrap marketplace focus function to enforce zero state regardless of focus (all or single)
  try {
    const originalApplyMarketplaceFocus = window.applyMarketplaceFocus;
    if (typeof originalApplyMarketplaceFocus === 'function' && !originalApplyMarketplaceFocus.__mockZeroWrapped) {
      const wrapped = function(selectedMarketplace, ...rest) {
        // Always perform the original focus change so UI state and classes update
        const result = originalApplyMarketplaceFocus.apply(this, [selectedMarketplace, ...rest]);
        // After UI updates, immediately clamp zero state again
        try {
          if (MockZeroData.isEnabled()) {
            // Ensure global focus is accurate
            try { window.globalMarketplaceFocus = selectedMarketplace || 'all'; } catch(_) {}
            // If ALL focus, restore ALL layout (marketplace rows etc.), then zero values
            setTimeout(() => {
              try {
                if ((selectedMarketplace || 'all') === 'all' && typeof window.showAllMarketplaceElements === 'function') {
                  window.showAllMarketplaceElements();
                }
              } catch(_) {}
              try { MockZeroData.applyDashboardZeroState(); } catch(_) {}
            }, 30);
          }
        } catch(_) {}
        return result;
      };
      wrapped.__mockZeroWrapped = true;
      window.applyMarketplaceFocus = wrapped;
    }
  } catch (_) {}

  // Also re-apply zeros after four sales cards refresh event
  window.addEventListener('fourSalesCardsRefreshed', () => {
    reapplyZeroSoon();
  });
  
  // Listings Status Overview: prevent hydration from mock data in zero mode
  try {
    const originalHydrateListingsStatusOverview = window.hydrateListingsStatusOverview;
    if (typeof originalHydrateListingsStatusOverview === 'function' && !originalHydrateListingsStatusOverview.__mockZeroWrapped) {
      const wrapped = function(selectedMarketplace = 'all') {
        if (!MockZeroData.isEnabled()) {
          return originalHydrateListingsStatusOverview.apply(this, arguments);
        }
        if (MockZeroData.appliedOnce) {
          try {
            document.querySelectorAll('.todays-sales-card-div, .yesterdays-sales-card-div').forEach((card) => {
              try { if (window.updateNoSalesStateForCard) window.updateNoSalesStateForCard(card, 0); } catch (_) {}
              const noSalesState = card.querySelector('.no-sales-state');
              const salesFilterDiv = card.querySelector('.sales-filter-div');
              const searchDiv = card.querySelector('.search-div');
              if (noSalesState) noSalesState.style.display = 'flex';
              if (salesFilterDiv) salesFilterDiv.style.display = 'none';
              if (searchDiv) searchDiv.style.display = 'none';
            });
          } catch (_) {}
          return;
        }
        // In zero mode, do not hydrate from mock data; ensure zeros instead
        reapplyZeroSoon();
      };
      wrapped.__mockZeroWrapped = true;
      window.hydrateListingsStatusOverview = wrapped;
    }
  } catch (_) {}

  // Four Sales Cards: prevent marketplace-dependent population in zero mode
  try {
    const originalFilterFourSalesCardsByMarketplace = window.filterFourSalesCardsByMarketplace;
    if (typeof originalFilterFourSalesCardsByMarketplace === 'function' && !originalFilterFourSalesCardsByMarketplace.__mockZeroWrapped) {
      const wrapped = function(marketplace) {
        if (!MockZeroData.isEnabled()) {
          return originalFilterFourSalesCardsByMarketplace.apply(this, arguments);
        }
        if (MockZeroData.appliedOnce) {
          try {
            document.querySelectorAll('.todays-sales-card-div, .yesterdays-sales-card-div').forEach((card) => {
              try { if (window.updateNoSalesStateForCard) window.updateNoSalesStateForCard(card, 0); } catch (_) {}
              const noSalesState = card.querySelector('.no-sales-state');
              const salesFilterDiv = card.querySelector('.sales-filter-div');
              const searchDiv = card.querySelector('.search-div');
              if (noSalesState) noSalesState.style.display = 'flex';
              if (salesFilterDiv) salesFilterDiv.style.display = 'none';
              if (searchDiv) searchDiv.style.display = 'none';
            });
          } catch (_) {}
          return;
        }
        // Enforce zero state on all four-sales cards
        document.querySelectorAll('.four-sales-cards-section .sales-card, .four-sales-cards-section .current-month-card-div, .four-sales-cards-section .last-month-card-div, .four-sales-cards-section .current-year-card-div, .four-sales-cards-section .last-year-card-div').forEach(card => {
          try { window.enforceZeroStateForSalesCard && window.enforceZeroStateForSalesCard(card); } catch (_) {}
        });
        reapplyZeroSoon();
      };
      wrapped.__mockZeroWrapped = true;
      window.filterFourSalesCardsByMarketplace = wrapped;
    }
  } catch (_) {}

  // Top Four Sales Cards: prevent updates in zero mode
  try {
    const originalUpdateTopFourCardsForMarketplace = window.updateTopFourCardsForMarketplace;
    if (typeof originalUpdateTopFourCardsForMarketplace === 'function' && !originalUpdateTopFourCardsForMarketplace.__mockZeroWrapped) {
      const wrapped = async function(selectedMarketplace, updateToken = null) {
        if (!MockZeroData.isEnabled()) {
          return originalUpdateTopFourCardsForMarketplace.apply(this, arguments);
        }
        if (MockZeroData.appliedOnce) {
          try {
            document.querySelectorAll('.todays-sales-card-div, .yesterdays-sales-card-div').forEach((card) => {
              if (window.updateNoSalesStateForCard) window.updateNoSalesStateForCard(card, 0);
            });
          } catch (_) {}
          return;
        }
        // Enforce zero on all top-four cards
        document.querySelectorAll('.top-four-sales-cards-section .top-day-card-div, .top-four-sales-cards-section .top-week-card-div, .top-four-sales-cards-section .top-month-card-div, .top-four-sales-cards-section .top-year-card-div').forEach(card => {
          try { window.enforceZeroStateForSalesCard && window.enforceZeroStateForSalesCard(card); } catch (_) {}
        });
        reapplyZeroSoon();
      };
      wrapped.__mockZeroWrapped = true;
      window.updateTopFourCardsForMarketplace = wrapped;
    }
  } catch (_) {}

  // Payout Cards: prevent data population on marketplace change in zero mode
  try {
    const originalUpdatePayoutDataForMarketplace = window.updatePayoutDataForMarketplace;
    if (typeof originalUpdatePayoutDataForMarketplace === 'function' && !originalUpdatePayoutDataForMarketplace.__mockZeroWrapped) {
      const wrapped = function(card, marketplace) {
        if (!MockZeroData.isEnabled()) {
          return originalUpdatePayoutDataForMarketplace.apply(this, arguments);
        }
        // In zero mode, always show empty payout state and hide data sections
        if (card && card.querySelector) {
          try {
            const emptyState = card.querySelector('.no-payout-state');
            const emptyMarketplaceState = card.querySelector('.no-payout-marketplace');
            const marketplacesDiv = card.querySelector('.marketplaces-div');
            const payoutDataDiv = card.querySelector('.payout-data-div');
            const summaryDiv = card.querySelector('.payout-summary-div');
            const dividers = card.querySelectorAll('.reviews-section-divider, .sales-section-divider');
            if (emptyState) emptyState.style.display = 'flex';
            if (emptyMarketplaceState) emptyMarketplaceState.style.display = 'none';
            if (marketplacesDiv) marketplacesDiv.style.display = 'none';
            if (payoutDataDiv) payoutDataDiv.style.display = 'none';
            if (summaryDiv) summaryDiv.style.display = 'none';
            if (dividers && dividers.length) dividers.forEach(d => d.style.display = 'none');
            // Ensure amounts are zeroed
            card.querySelectorAll('.payout-data-value').forEach(el => { el.textContent = (el.textContent.trim().charAt(0) === '£') ? '£0.00' : (el.textContent.trim().charAt(0) === '€') ? '€0.00' : (el.textContent.trim().charAt(0) === '¥') ? '¥0' : '$0.00'; });
          } catch (_) {}
        }
        reapplyZeroSoon();
      };
      wrapped.__mockZeroWrapped = true;
      window.updatePayoutDataForMarketplace = wrapped;
    }
  } catch (_) {}

  // --- Late-binding wrappers: ensure functions are wrapped even if defined after this script ---
  function wrapWhenAvailable(fnName, wrapperFactory, tries = 120) {
    if (!tries) return;
    try {
      const fn = window[fnName];
      if (typeof fn === 'function') {
        if (!fn.__mockZeroWrapped) {
          const wrapped = wrapperFactory(fn);
          if (typeof wrapped === 'function') {
            wrapped.__mockZeroWrapped = true;
            window[fnName] = wrapped;
          }
        }
        return;
      }
    } catch (_) {}
    setTimeout(() => wrapWhenAvailable(fnName, wrapperFactory, tries - 1), 50);
  }

  if (MockZeroData.isEnabled()) {
    wrapWhenAvailable('applyMarketplaceFocus', (orig) => function() { const r = orig.apply(this, arguments); reapplyZeroSoon(); return r; });
    wrapWhenAvailable('hydrateListingsStatusOverview', (orig) => function() { /* block in zero mode */ reapplyZeroSoon(); });
    wrapWhenAvailable('filterFourSalesCardsByMarketplace', (orig) => function() { document.querySelectorAll('.four-sales-cards-section .sales-card, .four-sales-cards-section .current-month-card-div, .four-sales-cards-section .last-month-card-div, .four-sales-cards-section .current-year-card-div, .four-sales-cards-section .last-year-card-div').forEach(card => { try { window.enforceZeroStateForSalesCard && window.enforceZeroStateForSalesCard(card); } catch (_) {} }); reapplyZeroSoon(); });
    wrapWhenAvailable('updateTopFourCardsForMarketplace', (orig) => function() { document.querySelectorAll('.top-four-sales-cards-section .top-day-card-div, .top-four-sales-cards-section .top-week-card-div, .top-four-sales-cards-section .top-month-card-div, .top-four-sales-cards-section .top-year-card-div').forEach(card => { try { window.enforceZeroStateForSalesCard && window.enforceZeroStateForSalesCard(card); } catch (_) {} }); reapplyZeroSoon(); });
    wrapWhenAvailable('updatePayoutDataForMarketplace', (orig) => function(card) { try { if (card && card.querySelector) { const es = card.querySelector('.no-payout-state'); const em = card.querySelector('.no-payout-marketplace'); const mp = card.querySelector('.marketplaces-div'); const pd = card.querySelector('.payout-data-div'); const sd = card.querySelector('.payout-summary-div'); const dvs = card.querySelectorAll('.reviews-section-divider, .sales-section-divider'); if (es) es.style.display = 'flex'; if (em) em.style.display = 'none'; if (mp) mp.style.display = 'none'; if (pd) pd.style.display = 'none'; if (sd) sd.style.display = 'none'; if (dvs && dvs.length) dvs.forEach(d => d.style.display = 'none'); } } catch (_) {} reapplyZeroSoon(); });
    wrapWhenAvailable('updatePayoutSummaryForMarketplace', (orig) => function() { const cards = document.querySelectorAll('.next-payout-card-div, .previous-payout-card-div'); cards.forEach(card => { const sd = card.querySelector('.payout-summary-div'); if (sd) sd.style.display = 'none'; }); reapplyZeroSoon(); });
    wrapWhenAvailable('updatePayoutSummaryForCard', (orig) => function(card) { if (card && card.querySelector) { const sd = card.querySelector('.payout-summary-div'); if (sd) sd.style.display = 'none'; } reapplyZeroSoon(); });

    // Sales card marketplace filtering/updaters — force sales-count to 0 in zero mode
    wrapWhenAvailable('applyMarketplaceDataFiltering', (orig) => function(salesCard, marketplace) {
      // Skip original and enforce zero state
      try {
        if (salesCard) {
          const sc = salesCard.querySelector('.sales-count');
          if (sc) { sc.textContent = '0'; sc.classList.add('zero'); sc.classList.remove('positive','negative','has-value'); }
          // Also zero inner metrics defensively
          ['royalties','returned','cancelled','new','ads'].forEach(cls => {
            const val = salesCard.querySelector(`.metric-col.${cls}-metric .metric-value`);
            const per = salesCard.querySelector(`.metric-col.${cls}-metric .metric-percentage`);
            if (val) { if (cls==='royalties') { const t=(val.textContent||'').trim(); const s=t.charAt(0); val.textContent = s==='£'?'£0.00':s==='€'?'€0.00':s==='¥'?'¥0':'$0.00'; } else { val.textContent='0'; } val.classList.add('zero'); val.classList.remove('positive','negative','has-value'); }
            if (per) { per.textContent='0.0%'; per.classList.add('zero'); per.classList.remove('positive','negative','has-value'); per.style.display='none'; }
          });
        }
      } catch(_) {}
      reapplyZeroSoon();
    });
    wrapWhenAvailable('filterSalesData', (orig) => function(marketplaceId, cardIndex){ const r = typeof orig==='function'?orig.apply(this, arguments):undefined; reapplyZeroSoon(); return r; });

    // Clamp listing visibility in zero mode so single-marketplace focus keeps empty state visible
    wrapWhenAvailable('updateListingVisibility', (orig) => function(salesCard, marketplace){
      if (!MockZeroData.isEnabled() || !salesCard) return typeof orig==='function' ? orig.apply(this, arguments) : undefined;
      try {
        const noSalesState = salesCard.querySelector('.no-sales-state');
        const listingDivs = salesCard.querySelectorAll('.listing-analytics-div');
        const listingDividers = salesCard.querySelectorAll('.listing-section-divider');
        const salesFilterDiv = salesCard.querySelector('.sales-filter-div');
        const searchDiv = salesCard.querySelector('.search-div');
        listingDivs.forEach(el => { el.style.display = 'none'; });
        listingDividers.forEach(el => { el.style.display = 'none'; });
        if (noSalesState) noSalesState.style.display = 'flex';
        if (salesFilterDiv) salesFilterDiv.style.display = 'none';
        if (searchDiv) searchDiv.style.display = 'none';
      } catch(_) {}
    });

    // Allow ALL-layout restoration to run, then immediately re-apply zero values
    wrapWhenAvailable('showAllMarketplaceElements', (orig) => function(){
      const r = typeof orig==='function' ? orig.apply(this, arguments) : undefined;
      if (MockZeroData.isEnabled()) { setTimeout(() => { try { MockZeroData.applyDashboardZeroState(); } catch(_) {} }, 10); }
      return r;
    });
    wrapWhenAvailable('restoreFourSalesCardsToAllMarketplaces', (orig) => function(){
      const r = typeof orig==='function' ? orig.apply(this, arguments) : undefined;
      if (MockZeroData.isEnabled()) { setTimeout(() => { try { MockZeroData.applyDashboardZeroState(); } catch(_) {} }, 10); }
      return r;
    });
    wrapWhenAvailable('recomputeSalesCountsForAllFocus', (orig) => function(){
      const r = typeof orig==='function' ? orig.apply(this, arguments) : undefined;
      if (MockZeroData.isEnabled()) { setTimeout(() => { try { MockZeroData.applyDashboardZeroState(); } catch(_) {} }, 10); }
      return r;
    });
    // Keep lifetime card in zero state after any clear attempts
    wrapWhenAvailable('clearZeroStateForLifetimeCard', (orig) => function(card){
      const r = typeof orig==='function' ? orig.apply(this, arguments) : undefined;
      if (MockZeroData.isEnabled()) { setTimeout(() => { try { MockZeroData.applyDashboardZeroState(); } catch(_) {} }, 10); }
      return r;
    });
  }

  // --- Mutation observer fallback: aggressively clamp to zero on any dashboard subtree changes ---
  if (MockZeroData.isEnabled()) {
    const root = document.querySelector('.dashboard-component') || document.body;
    if (root && window.MutationObserver) {
      let debounceId = null;
      let reapplyInProgress = false;
      const observer = new MutationObserver(() => {
        if (MockZeroData.appliedOnce || reapplyInProgress) return;
        clearTimeout(debounceId);
        debounceId = setTimeout(() => {
          try {
            reapplyInProgress = true;
            MockZeroData.applyDashboardZeroState();
          } finally {
            reapplyInProgress = false;
          }
        }, 40);
      });
      observer.observe(root, { childList: true, subtree: true });
      // Keep reference so we can disconnect after first apply
      MockZeroData._observerRef = observer;
    }
  }
  // Guard payout summary updates to zero while in mock zero mode
  try {
    const originalUpdatePayoutSummaryForMarketplace = window.updatePayoutSummaryForMarketplace;
    if (typeof originalUpdatePayoutSummaryForMarketplace === 'function' && !originalUpdatePayoutSummaryForMarketplace.__mockZeroWrapped) {
      const wrapped = function(marketplace) {
        if (!MockZeroData.isEnabled()) {
          return originalUpdatePayoutSummaryForMarketplace.apply(this, arguments);
        }
        const payoutCards = document.querySelectorAll('.next-payout-card-div, .previous-payout-card-div');
        payoutCards.forEach(card => {
          const summaryDiv = card.querySelector('.payout-summary-div');
          if (summaryDiv) summaryDiv.style.display = 'none';
        });
        reapplyZeroSoon();
      };
      wrapped.__mockZeroWrapped = true;
      window.updatePayoutSummaryForMarketplace = wrapped;
    }
  } catch (_) {}

  try {
    const originalUpdatePayoutSummaryForCard = window.updatePayoutSummaryForCard;
    if (typeof originalUpdatePayoutSummaryForCard === 'function' && !originalUpdatePayoutSummaryForCard.__mockZeroWrapped) {
      const wrapped = function(card, marketplace) {
        if (!MockZeroData.isEnabled()) {
          return originalUpdatePayoutSummaryForCard.apply(this, arguments);
        }
        const summaryDiv = card && card.querySelector ? card.querySelector('.payout-summary-div') : null;
        if (summaryDiv) summaryDiv.style.display = 'none';
        reapplyZeroSoon();
      };
      wrapped.__mockZeroWrapped = true;
      window.updatePayoutSummaryForCard = wrapped;
    }
  } catch (_) {}
})();


