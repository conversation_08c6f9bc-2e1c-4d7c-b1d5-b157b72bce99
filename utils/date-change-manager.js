/**
 * Date Change Manager for Snap Dashboard
 * Automatically detects timezone-aware date transitions and triggers refresh of date-dependent content
 * 
 * Features:
 * - Pacific Time (America/Los_Angeles) date change detection
 * - Adaptive timing: frequent checks near midnight, sparse during day
 * - Priority-based callback system for coordinated updates
 * - Memory-safe integration with EventCleanupManager
 * - Robust error handling and fallback mechanisms
 */

(function() {
  'use strict';

  class DateChangeManager {
    constructor() {
      this.isActive = false;
      this.lastKnownDate = null;
      this.checkInterval = null;
      this.callbacks = new Map(); // Map<callback, priority>
      this.isNearMidnight = false;
      
      // Timing configuration
      this.normalCheckFrequency = 5 * 60 * 1000; // 5 minutes during day
      this.midnightCheckFrequency = 30 * 1000; // 30 seconds near midnight
      this.midnightWindowMinutes = 10; // ±10 minutes around midnight for intensive checking
      
      // State tracking
      this.lastCheckTime = 0;
      this.consecutiveErrors = 0;
      this.maxConsecutiveErrors = 5;

      // Tab visibility tracking for inactive tab handling
      this.isTabVisible = !document.hidden;
      this.lastVisibilityChange = Date.now();
      this.missedChecksWhileHidden = 0;

      // Unified timer callback tracking
      this.checkCallbackId = null;
      
      console.log('📅 DateChangeManager initialized');
    }

    /**
     * Start monitoring for date changes
     */
    startMonitoring() {
      if (this.isActive) {
        console.warn('⚠️ DateChangeManager already active');
        return;
      }

      console.log('🚀 Starting date change monitoring...');
      this.isActive = true;
      this.consecutiveErrors = 0;

      // Initialize with current Pacific date
      this.updateLastKnownDate();

      // Start the monitoring loop
      this.scheduleNextCheck();

      // Set up visibility change monitoring for inactive tab handling
      this.setupVisibilityMonitoring();

      // Add immediate debug info
      const status = this.getStatus();
      console.log('✅ Date change monitoring started with status:', status);

      // Force an immediate check to verify everything is working
      setTimeout(() => {
        console.log('🔍 Performing initial date check...');
        this.forceCheck();
      }, 1000);
    }

    /**
     * Stop monitoring and cleanup
     */
    stopMonitoring() {
      if (!this.isActive) {
        return;
      }

      console.log('🛑 Stopping date change monitoring...');
      this.isActive = false;
      
      // Clear unified timer callback or regular interval
      if (this.checkCallbackId !== null && window.UnifiedTimerManager) {
        window.UnifiedTimerManager.unregisterCallback(this.checkCallbackId);
        this.checkCallbackId = null;
      } else if (this.checkInterval) {
        if (window.EventCleanupManager && typeof window.EventCleanupManager.clearInterval === 'function') {
          window.EventCleanupManager.clearInterval(this.checkInterval);
        } else {
          clearInterval(this.checkInterval);
        }
        this.checkInterval = null;
      }

      // Remove visibility change listener
      if (this.visibilityChangeHandler) {
        document.removeEventListener('visibilitychange', this.visibilityChangeHandler);
        this.visibilityChangeHandler = null;
      }

      console.log('✅ Date change monitoring stopped');
    }

    /**
     * Set up monitoring for tab visibility changes
     * This helps handle date changes when the browser tab is inactive
     */
    setupVisibilityMonitoring() {
      this.visibilityChangeHandler = () => {
        const wasVisible = this.isTabVisible;
        this.isTabVisible = !document.hidden;
        this.lastVisibilityChange = Date.now();

        console.log(`👁️ Tab visibility changed: ${wasVisible ? 'visible' : 'hidden'} → ${this.isTabVisible ? 'visible' : 'hidden'}`);

        if (!wasVisible && this.isTabVisible) {
          // Tab became visible - check if we missed any date changes
          console.log('🔍 Tab became visible - checking for missed date changes...');
          this.handleTabBecameVisible();
        }
      };

      document.addEventListener('visibilitychange', this.visibilityChangeHandler);
      console.log('👁️ Visibility monitoring enabled for inactive tab handling');
    }

    /**
     * Handle when tab becomes visible after being hidden
     * Check if date changed while tab was inactive
     */
    handleTabBecameVisible() {
      try {
        console.log('👁️ Tab became visible - performing comprehensive date check...');
        const currentDate = this.getCurrentPacificDateString();

        if (this.lastKnownDate && currentDate !== this.lastKnownDate) {
          console.log(`🎯 Date change detected while tab was hidden: ${this.lastKnownDate} → ${currentDate}`);

          // Trigger date change callbacks
          const previousDate = this.lastKnownDate;
          this.updateLastKnownDate();
          this.executeCallbacks(previousDate, currentDate);
        } else {
          console.log('✅ No date change detected while tab was hidden');
        }

        // Reset missed checks counter
        this.missedChecksWhileHidden = 0;

        // Ensure monitoring is still active - restart if needed
        if (!this.isActive) {
          console.log('🔄 DateChangeManager was inactive, restarting...');
          this.startMonitoring();
        } else {
          // Reschedule next check with current frequency
          this.scheduleNextCheck();
        }

      } catch (error) {
        console.error('❌ Error handling tab visibility change:', error);
        // If there's an error, try to restart monitoring
        if (!this.isActive) {
          setTimeout(() => {
            this.startMonitoring();
          }, 1000);
        }
      }
    }

    /**
     * Register a callback for date change notifications
     * @param {Function} callback - Function to call when date changes
     * @param {number} priority - Priority level (1 = highest, 10 = lowest)
     */
    registerCallback(callback, priority = 5) {
      if (typeof callback !== 'function') {
        console.error('❌ DateChangeManager: Callback must be a function');
        return;
      }

      this.callbacks.set(callback, priority);
      console.log(`📝 Registered date change callback with priority ${priority}`);
    }

    /**
     * Unregister a callback
     * @param {Function} callback - Function to remove
     */
    unregisterCallback(callback) {
      if (this.callbacks.has(callback)) {
        this.callbacks.delete(callback);
        console.log('🗑️ Unregistered date change callback');
      }
    }

    /**
     * Get current Pacific date as YYYY-MM-DD string
     */
    getCurrentPacificDateString() {
      try {
        if (window.SnapTimezone && typeof window.SnapTimezone.getCurrentPacificDateString === 'function') {
          return window.SnapTimezone.getCurrentPacificDateString();
        } else if (window.SnapTimezone && typeof window.SnapTimezone.getPacificDate === 'function') {
          const pacificDate = window.SnapTimezone.getPacificDate();
          return pacificDate.toISOString().split('T')[0]; // YYYY-MM-DD
        } else {
          // Fallback to local time if timezone utility not available
          const localDate = new Date();
          return localDate.toISOString().split('T')[0];
        }
      } catch (error) {
        console.error('❌ Error getting Pacific date:', error);
        // Emergency fallback
        return new Date().toISOString().split('T')[0];
      }
    }

    /**
     * Update the stored last known date
     */
    updateLastKnownDate() {
      this.lastKnownDate = this.getCurrentPacificDateString();
      console.log(`📅 Updated last known date: ${this.lastKnownDate}`);
    }

    /**
     * Check if we're near midnight Pacific Time
     */
    isNearMidnightPacific() {
      try {
        if (window.SnapTimezone && typeof window.SnapTimezone.getPacificTime === 'function') {
          const pacificTime = window.SnapTimezone.getPacificTime();
          const hour = pacificTime.getHours();
          const minute = pacificTime.getMinutes();
          
          // Check if within 10 minutes of midnight (23:50-00:10)
          return (hour === 23 && minute >= 50) || (hour === 0 && minute <= 10);
        }
        return false;
      } catch (error) {
        console.error('❌ Error checking midnight proximity:', error);
        return false;
      }
    }

    /**
     * Core date change detection logic
     */
    checkDateChange() {
      try {
        const currentDate = this.getCurrentPacificDateString();

        // Log periodic status for debugging (every 5 minutes)
        if (Date.now() - this.lastCheckTime > 300000) {
          console.log(`📅 DateChangeManager status check - Current: ${currentDate}, Last known: ${this.lastKnownDate}, Active: ${this.isActive}`);
        }

        // Check if date has changed
        if (this.lastKnownDate && currentDate !== this.lastKnownDate) {
          console.log(`🎯 Date change detected: ${this.lastKnownDate} → ${currentDate}`);

          // Update stored date
          const previousDate = this.lastKnownDate;
          this.lastKnownDate = currentDate;

          // Trigger date change event
          this.triggerDateChangeEvent(previousDate, currentDate);
        }

        // Update timing strategy based on proximity to midnight
        const nearMidnight = this.isNearMidnightPacific();
        if (nearMidnight !== this.isNearMidnight) {
          this.isNearMidnight = nearMidnight;
          console.log(`⏰ Midnight proximity changed: ${nearMidnight ? 'entering' : 'leaving'} midnight window`);

          // Reschedule with new frequency
          this.scheduleNextCheck();
        }

        // Reset error count on successful check
        this.consecutiveErrors = 0;
        this.lastCheckTime = Date.now();

        // Ensure continuous monitoring - verify UnifiedTimerManager callback is still active
        if (window.UnifiedTimerManager && this.checkCallbackId !== null) {
          const callbackInfo = window.UnifiedTimerManager.getCallbackInfo();
          const isRegistered = callbackInfo.some(info => info.id === this.checkCallbackId);

          if (!isRegistered) {
            console.warn('⚠️ DateChangeManager callback not found in UnifiedTimerManager, re-registering...');
            this.scheduleNextCheck();
          }
        }

      } catch (error) {
        console.error('❌ Error in date change check:', error);
        this.consecutiveErrors++;

        // If too many consecutive errors, restart monitoring instead of stopping
        if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
          console.error(`❌ Too many consecutive errors (${this.consecutiveErrors}), restarting monitoring...`);
          this.stopMonitoring();
          setTimeout(() => {
            if (!this.isActive) { // Only restart if not already active
              console.log('🔄 Restarting DateChangeManager after errors...');
              this.startMonitoring();
            }
          }, 5000); // Wait 5 seconds before restarting
        } else {
          // For non-critical errors, ensure monitoring continues
          setTimeout(() => {
            if (this.isActive) {
              this.scheduleNextCheck();
            }
          }, 10000); // Retry in 10 seconds
        }
      }
    }

    /**
     * Trigger date change event and notify all callbacks
     */
    async triggerDateChangeEvent(previousDate, currentDate) {
      console.log(`🔔 Triggering date change event: ${previousDate} → ${currentDate}`);
      
      // Sort callbacks by priority (1 = highest priority)
      const sortedCallbacks = Array.from(this.callbacks.entries())
        .sort(([, priorityA], [, priorityB]) => priorityA - priorityB);
      
      // Execute callbacks in priority order
      for (const [callback, priority] of sortedCallbacks) {
        try {
          console.log(`📞 Executing date change callback (priority ${priority})`);
          
          // Support both sync and async callbacks
          const result = callback(previousDate, currentDate);
          if (result && typeof result.then === 'function') {
            await result;
          }
          
        } catch (error) {
          console.error(`❌ Error in date change callback (priority ${priority}):`, error);
          // Continue with other callbacks even if one fails
        }
      }
      
      console.log('✅ Date change event processing completed');
    }

    /**
     * Schedule the next date check with adaptive timing
     */
    scheduleNextCheck() {
      if (!this.isActive) {
        return;
      }

      // Clear any existing interval or callback first
      if (this.checkCallbackId !== null && window.UnifiedTimerManager) {
        window.UnifiedTimerManager.unregisterCallback(this.checkCallbackId);
        this.checkCallbackId = null;
      } else if (this.checkInterval) {
        if (window.EventCleanupManager && typeof window.EventCleanupManager.clearInterval === 'function') {
          window.EventCleanupManager.clearInterval(this.checkInterval);
        } else {
          clearInterval(this.checkInterval);
        }
        this.checkInterval = null;
      }

      // Determine check frequency based on proximity to midnight and tab visibility
      let frequency;
      let mode;

      if (!this.isTabVisible) {
        // Tab is hidden - use longer intervals to conserve resources
        // but still check periodically in case of date changes
        frequency = this.isNearMidnight ? 30000 : 120000; // 30s near midnight, 2min normal
        mode = `hidden tab ${this.isNearMidnight ? 'midnight' : 'normal'}`;
      } else {
        // Tab is visible - use normal frequencies
        frequency = this.isNearMidnight ?
          this.midnightCheckFrequency :
          this.normalCheckFrequency;
        mode = this.isNearMidnight ? 'midnight mode' : 'normal mode';
      }

      console.log(`⏰ Scheduling next check in ${frequency}ms (${mode})`);

      // Use UnifiedTimerManager for better performance if available
      if (window.UnifiedTimerManager) {
        // Unregister previous callback if exists
        if (this.checkCallbackId !== null) {
          window.UnifiedTimerManager.unregisterCallback(this.checkCallbackId);
        }

        // Register new callback with current frequency
        this.checkCallbackId = window.UnifiedTimerManager.registerCallback(
          () => this.checkDateChange(),
          frequency,
          `dateChangeManager_${mode.replace(/\s+/g, '_')}`
        );

        // Start unified timer if not already running
        if (!window.UnifiedTimerManager.isActive) {
          window.UnifiedTimerManager.start();
        }

        console.log(`⏰ Date change monitoring registered with UnifiedTimerManager (${mode})`);
      } else {
        // Fallback to EventCleanupManager
        if (window.EventCleanupManager) {
          this.checkInterval = window.EventCleanupManager.setInterval(() => {
            this.checkDateChange();
            // Don't reschedule here - let the check determine if frequency needs to change
          }, frequency);
        } else {
          // Final fallback to regular setInterval
          console.warn('⚠️ EventCleanupManager not available, using regular setInterval');
          this.checkInterval = setInterval(() => {
            this.checkDateChange();
            // Don't reschedule here - let the check determine if frequency needs to change
          }, frequency);
        }

        console.log(`⏰ Date change monitoring scheduled with individual timer (${mode})`);
      }
    }

    /**
     * Get manager status for debugging
     */
    getStatus() {
      return {
        isActive: this.isActive,
        lastKnownDate: this.lastKnownDate,
        isNearMidnight: this.isNearMidnight,
        callbackCount: this.callbacks.size,
        consecutiveErrors: this.consecutiveErrors,
        lastCheckTime: this.lastCheckTime,
        timeSinceLastCheck: Date.now() - this.lastCheckTime,
        isTabVisible: this.isTabVisible,
        lastVisibilityChange: this.lastVisibilityChange,
        missedChecksWhileHidden: this.missedChecksWhileHidden
      };
    }

    /**
     * Get comprehensive diagnostic information
     */
    getDiagnostics() {
      const status = this.getStatus();
      const currentDate = this.getCurrentPacificDateString();
      const pacificTime = window.SnapTimezone ? window.SnapTimezone.getPacificTime() : new Date();

      return {
        ...status,
        currentPacificDate: currentDate,
        currentPacificTime: pacificTime.toISOString(),
        unifiedTimerManagerAvailable: !!window.UnifiedTimerManager,
        unifiedTimerManagerActive: window.UnifiedTimerManager ? window.UnifiedTimerManager.isActive : false,
        eventCleanupManagerAvailable: !!window.EventCleanupManager,
        checkCallbackId: this.checkCallbackId,
        registeredCallbacks: Array.from(this.callbacks.entries()).map(([callback, priority]) => ({
          priority,
          functionName: callback.name || 'anonymous'
        })),
        timezoneInfo: {
          browserTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          pacificOffset: pacificTime.getTimezoneOffset()
        }
      };
    }

    /**
     * Force a date change check (for testing)
     */
    forceCheck() {
      console.log('🧪 Forcing date change check...');
      this.checkDateChange();
    }

    /**
     * Perform a comprehensive system check and restart if needed
     */
    performHealthCheck() {
      console.log('🏥 Performing DateChangeManager health check...');

      try {
        const diagnostics = this.getDiagnostics();
        console.log('📊 DateChangeManager diagnostics:', diagnostics);

        // Check if monitoring should be active but isn't
        if (!this.isActive) {
          console.log('🔄 DateChangeManager is inactive, starting monitoring...');
          this.startMonitoring();
          return;
        }

        // Check if UnifiedTimerManager callback is missing
        if (window.UnifiedTimerManager && this.checkCallbackId !== null) {
          const callbackInfo = window.UnifiedTimerManager.getCallbackInfo();
          const isRegistered = callbackInfo.some(info => info.id === this.checkCallbackId);

          if (!isRegistered) {
            console.log('🔄 UnifiedTimerManager callback missing, re-registering...');
            this.scheduleNextCheck();
          }
        }

        // Check if it's been too long since last check
        const timeSinceLastCheck = Date.now() - this.lastCheckTime;
        const maxInterval = this.isNearMidnight ? 60000 : 600000; // 1 min near midnight, 10 min normal

        if (timeSinceLastCheck > maxInterval) {
          console.log(`⚠️ Too long since last check (${Math.round(timeSinceLastCheck/1000)}s), forcing check...`);
          this.forceCheck();
        }

        console.log('✅ DateChangeManager health check completed');

      } catch (error) {
        console.error('❌ Error during health check:', error);
        // Try to restart monitoring
        this.stopMonitoring();
        setTimeout(() => {
          this.startMonitoring();
        }, 2000);
      }
    }

    /**
     * Simulate a date change (for testing only - disabled in production)
     */
    simulateDateChange(newDate) {
      console.warn('🚫 simulateDateChange is disabled - using real Pacific Time only');
      console.log('💡 Use forceDateChangeCheck() to manually trigger a real date check');
      return;
    }

  /**
   * Production method: Trigger data refresh for real Amazon API integration
   * This method should be called by production callbacks to refresh live data
   */
  async triggerDataRefresh(dataType, options = {}) {
    console.log(`📡 Triggering data refresh for: ${dataType}`);

    try {
      // This will be implemented when integrating with real Amazon APIs
      switch (dataType) {
        case 'sales-data':
          // await refreshSalesDataFromAmazon(options);
          console.log('🔄 Sales data refresh triggered');
          break;

        case 'listings-data':
          // await refreshListingsDataFromAmazon(options);
          console.log('🔄 Listings data refresh triggered');
          break;

        case 'ad-spend-data':
          // await refreshAdSpendDataFromAmazon(options);
          console.log('🔄 Ad spend data refresh triggered');
          break;

        default:
          console.warn(`⚠️ Unknown data type for refresh: ${dataType}`);
      }
    } catch (error) {
      console.error(`❌ Error refreshing ${dataType}:`, error);
      throw error; // Re-throw to allow callback error handling
    }
  }
  }

  // Create singleton instance and attach to window
  const dateChangeManager = new DateChangeManager();
  window.DateChangeManager = dateChangeManager;

  // Expose class for potential multiple instances
  window.DateChangeManagerClass = DateChangeManager;

  // Expose global helper functions for debugging and maintenance
  window.checkDateChangeManagerHealth = function() {
    if (window.DateChangeManager) {
      window.DateChangeManager.performHealthCheck();
    } else {
      console.error('❌ DateChangeManager not available');
    }
  };

  window.getDateChangeManagerDiagnostics = function() {
    if (window.DateChangeManager) {
      return window.DateChangeManager.getDiagnostics();
    } else {
      console.error('❌ DateChangeManager not available');
      return null;
    }
  };

  window.forceDateChangeCheck = function() {
    if (window.DateChangeManager) {
      window.DateChangeManager.forceCheck();
    } else {
      console.error('❌ DateChangeManager not available');
    }
  };

  // Auto-perform health check every 10 minutes to ensure system stays healthy
  if (window.EventCleanupManager) {
    window.EventCleanupManager.setInterval(() => {
      if (window.DateChangeManager && window.DateChangeManager.isActive) {
        window.DateChangeManager.performHealthCheck();
      }
    }, 600000); // 10 minutes
  }

  // Production verification function
  window.verifyDateChangeManagerProduction = function() {
    console.log('🔍 Verifying DateChangeManager production setup...');

    if (!window.DateChangeManager) {
      console.error('❌ DateChangeManager not available');
      return false;
    }

    const diagnostics = window.DateChangeManager.getDiagnostics();
    const currentPacificDate = window.SnapTimezone ?
      window.SnapTimezone.getCurrentPacificDateString() :
      new Date().toISOString().split('T')[0];

    console.log('✅ DateChangeManager Status:');
    console.log(`   - Active: ${diagnostics.isActive}`);
    console.log(`   - Current Pacific Date: ${diagnostics.currentPacificDate}`);
    console.log(`   - Registered Callbacks: ${diagnostics.registeredCallbacks.length}`);
    console.log(`   - Using Real Time: ${diagnostics.currentPacificDate === currentPacificDate ? 'Yes' : 'No'}`);
    console.log(`   - Next Check Mode: ${diagnostics.isNearMidnight ? 'Frequent (near midnight)' : 'Standard'}`);

    if (diagnostics.isActive && diagnostics.registeredCallbacks.length > 0) {
      console.log('🎉 DateChangeManager is properly configured for production!');
      return true;
    } else {
      console.warn('⚠️ DateChangeManager may not be fully configured');
      return false;
    }
  };

  console.log('📅 DateChangeManager utility loaded and available globally');

})();
