/**
 * Verification script for compare mode label centering fix
 * This script tests the logic for centering X-axis labels under main columns
 * when compare mode is enabled in stacked column charts.
 */

// Mock positioning info for compare mode
const comparePositioningInfo = {
  hasCompare: true,
  mainColumnWidth: 32,
  comparisonColumnWidth: 32,
  comparisonOffset: 16,
  sidePadding: 40,
  columnGroupWidth: 80, // 32 + 16 + 32
  gapBetweenGroups: 24
};

// Mock positioning info for normal mode
const normalPositioningInfo = {
  hasCompare: false,
  mainColumnWidth: 32,
  columnGroupWidth: 32,
  sidePadding: 40,
  gapBetweenGroups: 24
};

// Mock group data
const mockGroup = {
  startIndex: 0,
  label: 'JAN',
  sublabel: "'25"
};

/**
 * Calculate group start position (simplified version of the actual function)
 */
function calculateGroupStartPosition(group, positioningInfo) {
  return positioningInfo.sidePadding + (group.startIndex * (positioningInfo.columnGroupWidth + positioningInfo.gapBetweenGroups));
}

/**
 * Calculate label X position (the fixed logic)
 */
function calculateLabelX(groupStartX, positioningInfo) {
  if (positioningInfo.hasCompare && positioningInfo.mainColumnWidth) {
    // In compare mode: center labels under main column only
    // Main column is positioned after comparison column and gap
    const comparisonColumnWidth = positioningInfo.comparisonColumnWidth || positioningInfo.mainColumnWidth;
    const comparisonOffset = positioningInfo.comparisonOffset || 16;
    const mainColumnX = groupStartX + comparisonColumnWidth + comparisonOffset;
    return mainColumnX + (positioningInfo.mainColumnWidth / 2);
  } else {
    // Normal mode: use the appropriate column width
    const columnWidth = positioningInfo.columnWidth || positioningInfo.columnGroupWidth || 24;
    return groupStartX + (columnWidth / 2);
  }
}

/**
 * Test the fix
 */
function testLabelCentering() {
  console.log('=== Testing Compare Mode Label Centering Fix ===\n');
  
  // Test compare mode
  console.log('COMPARE MODE TEST:');
  const compareGroupStartX = calculateGroupStartPosition(mockGroup, comparePositioningInfo);
  const compareLabelX = calculateLabelX(compareGroupStartX, comparePositioningInfo);
  
  console.log(`Group start X (comparison column): ${compareGroupStartX}`);
  console.log(`Comparison column width: ${comparePositioningInfo.comparisonColumnWidth}`);
  console.log(`Comparison offset (gap): ${comparePositioningInfo.comparisonOffset}`);
  console.log(`Main column start X: ${compareGroupStartX + comparePositioningInfo.comparisonColumnWidth + comparePositioningInfo.comparisonOffset}`);
  console.log(`Main column center X: ${compareLabelX}`);
  console.log(`Expected main column center: ${compareGroupStartX + comparePositioningInfo.comparisonColumnWidth + comparePositioningInfo.comparisonOffset + (comparePositioningInfo.mainColumnWidth / 2)}`);
  
  // Verify the calculation
  const expectedMainColumnCenter = compareGroupStartX + comparePositioningInfo.comparisonColumnWidth + comparePositioningInfo.comparisonOffset + (comparePositioningInfo.mainColumnWidth / 2);
  const isCorrect = compareLabelX === expectedMainColumnCenter;
  console.log(`✅ Compare mode calculation: ${isCorrect ? 'CORRECT' : 'INCORRECT'}\n`);
  
  // Test normal mode
  console.log('NORMAL MODE TEST:');
  const normalGroupStartX = calculateGroupStartPosition(mockGroup, normalPositioningInfo);
  const normalLabelX = calculateLabelX(normalGroupStartX, normalPositioningInfo);
  
  console.log(`Group start X: ${normalGroupStartX}`);
  console.log(`Column width: ${normalPositioningInfo.columnGroupWidth}`);
  console.log(`Label center X: ${normalLabelX}`);
  console.log(`Expected center: ${normalGroupStartX + (normalPositioningInfo.columnGroupWidth / 2)}`);
  
  // Verify the calculation
  const expectedNormalCenter = normalGroupStartX + (normalPositioningInfo.columnGroupWidth / 2);
  const isNormalCorrect = normalLabelX === expectedNormalCenter;
  console.log(`✅ Normal mode calculation: ${isNormalCorrect ? 'CORRECT' : 'INCORRECT'}\n`);
  
  // Summary
  console.log('=== SUMMARY ===');
  console.log(`Compare mode fix: ${isCorrect ? '✅ WORKING' : '❌ BROKEN'}`);
  console.log(`Normal mode compatibility: ${isNormalCorrect ? '✅ WORKING' : '❌ BROKEN'}`);
  console.log(`Overall fix status: ${isCorrect && isNormalCorrect ? '✅ SUCCESS' : '❌ NEEDS WORK'}`);
  
  return isCorrect && isNormalCorrect;
}

// Run the test
if (typeof window !== 'undefined') {
  // Browser environment
  window.addEventListener('DOMContentLoaded', testLabelCentering);
} else {
  // Node.js environment
  testLabelCentering();
}
