/**
 * Daily Sales History Manager
 * Specialized for handling massive scale daily sales data (billions of records)
 */

class DailySalesManager {
  constructor() {
    this.dbManager = window.IndexedDBManager;
    this.batchSize = 1000; // Process 1000 records at a time
    this.compressionEnabled = true;
    this.aggregationCache = new Map(); // Cache for aggregated data
    this.maxCacheSize = 10000; // Cache 10K aggregated results
  }

  /**
   * Store daily sales data efficiently
   */
  async storeDailySales(asin, date, salesData) {
    try {
      const record = {
        asin: asin,
        date: date,
        sales: salesData.sales || 0,
        returns: salesData.returns || 0,
        cancelled: salesData.cancelled || 0,
        royalties: salesData.royalties || 0,
        marketplace: salesData.marketplace || 'US',
        timestamp: Date.now(),
        expiry: Date.now() + (10 * 365 * 24 * 60 * 60 * 1000) // 10 years
      };

      // Compress data if enabled
      if (this.compressionEnabled) {
        record.compressed = this.compressSalesData(record);
      }

      await this.dbManager.storeAmazonData('dailySalesHistory', record);
      
      // Update aggregated metrics
      await this.updateAggregatedMetrics(asin, salesData);
      
    } catch (error) {
      console.error(`❌ Failed to store daily sales for ${asin}:`, error);
      throw error;
    }
  }

  /**
   * Get daily sales history for an ASIN (with date range)
   */
  async getDailySalesHistory(asin, startDate = null, endDate = null) {
    try {
      const cacheKey = `${asin}_${startDate}_${endDate}`;
      
      // Check cache first
      if (this.aggregationCache.has(cacheKey)) {
        return this.aggregationCache.get(cacheKey);
      }

      const transaction = this.dbManager.db.transaction(['dailySalesHistory'], 'readonly');
      const store = transaction.objectStore('dailySalesHistory');
      const index = store.index('asin');
      
      const results = [];
      const cursor = await index.openCursor(IDBKeyRange.only(asin));
      
      while (cursor) {
        const record = cursor.value;
        
        // Filter by date range if specified
        if (startDate && record.date < startDate) {
          await cursor.continue();
          continue;
        }
        if (endDate && record.date > endDate) {
          await cursor.continue();
          continue;
        }
        
        // Decompress if needed
        const salesData = this.compressionEnabled ? 
          this.decompressSalesData(record) : record;
        
        results.push(salesData);
        await cursor.continue();
      }
      
      // Cache result
      this.setCacheWithLimit(cacheKey, results);
      
      return results;
      
    } catch (error) {
      console.error(`❌ Failed to get daily sales history for ${asin}:`, error);
      throw error;
    }
  }

  /**
   * Get aggregated sales metrics for an ASIN
   */
  async getAggregatedMetrics(asin) {
    try {
      const cacheKey = `metrics_${asin}`;
      
      if (this.aggregationCache.has(cacheKey)) {
        return this.aggregationCache.get(cacheKey);
      }

      const metrics = await this.dbManager.getData('productMetrics', asin);
      
      if (metrics) {
        this.setCacheWithLimit(cacheKey, metrics);
        return metrics;
      }
      
      // If no cached metrics, calculate from daily data
      return await this.calculateAggregatedMetrics(asin);
      
    } catch (error) {
      console.error(`❌ Failed to get aggregated metrics for ${asin}:`, error);
      throw error;
    }
  }

  /**
   * Calculate aggregated metrics from daily sales data
   */
  async calculateAggregatedMetrics(asin) {
    try {
      const dailyData = await this.getDailySalesHistory(asin);
      
      const metrics = {
        asin: asin,
        totalSales: 0,
        totalReturns: 0,
        totalCancelled: 0,
        totalRoyalties: 0,
        averageDailySales: 0,
        bestDay: { date: null, sales: 0 },
        worstDay: { date: null, sales: Infinity },
        lastSaleDate: null,
        firstSaleDate: null,
        activeDays: 0,
        marketplaces: new Set(),
        lastUpdated: Date.now()
      };

      for (const record of dailyData) {
        metrics.totalSales += record.sales;
        metrics.totalReturns += record.returns;
        metrics.totalCancelled += record.cancelled;
        metrics.totalRoyalties += record.royalties;
        metrics.marketplaces.add(record.marketplace);
        
        if (record.sales > 0) {
          metrics.activeDays++;
          
          if (!metrics.firstSaleDate || record.date < metrics.firstSaleDate) {
            metrics.firstSaleDate = record.date;
          }
          
          if (!metrics.lastSaleDate || record.date > metrics.lastSaleDate) {
            metrics.lastSaleDate = record.date;
          }
        }
        
        if (record.sales > metrics.bestDay.sales) {
          metrics.bestDay = { date: record.date, sales: record.sales };
        }
        
        if (record.sales < metrics.worstDay.sales) {
          metrics.worstDay = { date: record.date, sales: record.sales };
        }
      }
      
      metrics.averageDailySales = metrics.activeDays > 0 ? 
        metrics.totalSales / metrics.activeDays : 0;
      
      // Convert Set to Array for storage
      metrics.marketplaces = Array.from(metrics.marketplaces);
      
      // Store calculated metrics
      await this.dbManager.storeAmazonData('productMetrics', metrics);
      
      return metrics;
      
    } catch (error) {
      console.error(`❌ Failed to calculate aggregated metrics for ${asin}:`, error);
      throw error;
    }
  }

  /**
   * Update aggregated metrics incrementally
   */
  async updateAggregatedMetrics(asin, newSalesData) {
    try {
      let metrics = await this.dbManager.getData('productMetrics', asin);
      
      if (!metrics) {
        // Create new metrics record
        metrics = {
          asin: asin,
          totalSales: 0,
          totalReturns: 0,
          totalCancelled: 0,
          totalRoyalties: 0,
          activeDays: 0,
          marketplaces: [],
          lastUpdated: Date.now()
        };
      }
      
      // Update metrics
      metrics.totalSales += newSalesData.sales || 0;
      metrics.totalReturns += newSalesData.returns || 0;
      metrics.totalCancelled += newSalesData.cancelled || 0;
      metrics.totalRoyalties += newSalesData.royalties || 0;
      
      if (newSalesData.sales > 0) {
        metrics.activeDays++;
      }
      
      if (newSalesData.marketplace && !metrics.marketplaces.includes(newSalesData.marketplace)) {
        metrics.marketplaces.push(newSalesData.marketplace);
      }
      
      metrics.lastUpdated = Date.now();
      
      // Store updated metrics
      await this.dbManager.storeAmazonData('productMetrics', metrics);
      
      // Clear cache for this ASIN
      this.clearCacheForAsin(asin);
      
    } catch (error) {
      console.error(`❌ Failed to update aggregated metrics for ${asin}:`, error);
    }
  }

  /**
   * Compress sales data to save storage space
   */
  compressSalesData(record) {
    // Simple compression: store only non-zero values
    const compressed = {};
    
    if (record.sales > 0) compressed.s = record.sales;
    if (record.returns > 0) compressed.r = record.returns;
    if (record.cancelled > 0) compressed.c = record.cancelled;
    if (record.royalties > 0) compressed.roy = record.royalties;
    
    return compressed;
  }

  /**
   * Decompress sales data
   */
  decompressSalesData(record) {
    if (!record.compressed) return record;
    
    return {
      ...record,
      sales: record.compressed.s || 0,
      returns: record.compressed.r || 0,
      cancelled: record.compressed.c || 0,
      royalties: record.compressed.roy || 0
    };
  }

  /**
   * Set cache with size limit
   */
  setCacheWithLimit(key, value) {
    if (this.aggregationCache.size >= this.maxCacheSize) {
      // Remove oldest entry (simple LRU)
      const firstKey = this.aggregationCache.keys().next().value;
      this.aggregationCache.delete(firstKey);
    }
    
    this.aggregationCache.set(key, value);
  }

  /**
   * Clear cache entries for specific ASIN
   */
  clearCacheForAsin(asin) {
    for (const key of this.aggregationCache.keys()) {
      if (key.startsWith(asin) || key.startsWith(`metrics_${asin}`)) {
        this.aggregationCache.delete(key);
      }
    }
  }

  /**
   * Batch process multiple daily sales records
   */
  async batchStoreDailySales(salesRecords) {
    const batches = [];
    
    // Split into batches
    for (let i = 0; i < salesRecords.length; i += this.batchSize) {
      batches.push(salesRecords.slice(i, i + this.batchSize));
    }
    
    console.log(`📦 Processing ${salesRecords.length} sales records in ${batches.length} batches...`);
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      
      try {
        // Process batch
        for (const record of batch) {
          await this.storeDailySales(record.asin, record.date, record);
        }
        
        console.log(`✅ Processed batch ${i + 1}/${batches.length} (${batch.length} records)`);
        
        // Small delay to prevent blocking
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
      } catch (error) {
        console.error(`❌ Failed to process batch ${i + 1}:`, error);
      }
    }
    
    console.log(`✅ Batch processing completed: ${salesRecords.length} records`);
  }

  /**
   * Get storage statistics for daily sales
   */
  async getStorageStats() {
    try {
      const stats = await this.dbManager.getStorageStats();
      
      return {
        dailySalesRecords: stats.dailySalesHistory?.recordCount || 0,
        productMetrics: stats.productMetrics?.recordCount || 0,
        cacheSize: this.aggregationCache.size,
        maxCacheSize: this.maxCacheSize,
        compressionEnabled: this.compressionEnabled
      };
      
    } catch (error) {
      console.error('❌ Failed to get storage stats:', error);
      return null;
    }
  }
}

// Global instance
window.DailySalesManager = new DailySalesManager();

// Export for global use
window.storeDailySales = (asin, date, data) => window.DailySalesManager.storeDailySales(asin, date, data);
window.getDailySalesHistory = (asin, start, end) => window.DailySalesManager.getDailySalesHistory(asin, start, end);
window.getAggregatedMetrics = (asin) => window.DailySalesManager.getAggregatedMetrics(asin);
window.batchStoreDailySales = (records) => window.DailySalesManager.batchStoreDailySales(records);
