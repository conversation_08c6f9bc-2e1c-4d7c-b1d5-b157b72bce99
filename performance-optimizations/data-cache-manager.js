/**
 * Data Cache Manager for Snap Dashboard
 * Implements intelligent caching to prevent redundant data generation
 */

class DataCacheManager {
  constructor() {
    this.cache = new Map();
    this.maxCacheSize = 50; // Limit cache size
    this.cacheHits = 0;
    this.cacheMisses = 0;
    this.maxCounters = 10000; // Prevent infinite counter growth
    this.cacheExpiry = 30 * 60 * 1000; // 30 minutes cache expiry
  }

  /**
   * Generate cache key for data requests
   */
  generateCacheKey(type, params) {
    return `${type}_${JSON.stringify(params)}`;
  }

  /**
   * Get cached data or generate new data
   */
  async getData(type, params, generatorFunction) {
    const cacheKey = this.generateCacheKey(type, params);
    
    // Check cache first and validate expiry
    if (this.cache.has(cacheKey)) {
      const cachedItem = this.cache.get(cacheKey);
      const now = Date.now();

      // Check if cache item has expired
      if (now - cachedItem.timestamp > this.cacheExpiry) {
        this.cache.delete(cacheKey);
      } else {
        this.cacheHits++;
        this.resetCountersIfNeeded();
        if (window.SnapLogger) {
          window.SnapLogger.verbose(`📦 Cache HIT for ${type}:`, { hits: this.cacheHits, misses: this.cacheMisses });
        }
        return cachedItem.data;
      }
    }

    // Cache miss - generate new data
    this.cacheMisses++;
    this.resetCountersIfNeeded();
    if (window.SnapLogger) {
      window.SnapLogger.verbose(`🔄 Cache MISS for ${type}:`, { hits: this.cacheHits, misses: this.cacheMisses });
    }
    
    const data = await generatorFunction(params);
    
    // Store in cache with LRU eviction
    this.setCache(cacheKey, data);
    
    return data;
  }

  /**
   * Set cache with LRU eviction and timestamp
   */
  setCache(key, data) {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    // Store data with timestamp for expiry checking
    this.cache.set(key, {
      data: data,
      timestamp: Date.now()
    });
  }

  /**
   * Reset counters if they get too large to prevent memory issues
   */
  resetCountersIfNeeded() {
    if (this.cacheHits > this.maxCounters || this.cacheMisses > this.maxCounters) {
      if (window.SnapLogger) {
        window.SnapLogger.debug('🔄 Resetting cache counters to prevent overflow');
      }
      this.cacheHits = Math.floor(this.cacheHits / 2);
      this.cacheMisses = Math.floor(this.cacheMisses / 2);
    }
  }

  /**
   * Clear cache for specific data type
   */
  clearCache(type = null) {
    if (type) {
      // Clear specific type
      for (const key of this.cache.keys()) {
        if (key.startsWith(type)) {
          this.cache.delete(key);
        }
      }
    } else {
      // Clear all cache
      this.cache.clear();
    }
    if (window.SnapLogger) {
      window.SnapLogger.debug(`🗑️ Cache cleared for: ${type || 'all'}`);
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hits: this.cacheHits,
      misses: this.cacheMisses,
      hitRate: this.cacheHits / (this.cacheHits + this.cacheMisses) || 0
    };
  }
}

// Global cache instance
window.DataCacheManager = new DataCacheManager();

/**
 * Optimized data generation functions with caching
 */
async function generateTodayVsPreviousYearsDataCached() {
  // Zero-data mock mode: short-circuit with empty dataset
  if (window.MockZeroData && window.MockZeroData.isEnabled()) {
    return window.MockZeroData.getZeroTodayVsPreviousYearsData();
  }
  const params = {
    startYear: 2000,
    endYear: 2025,
    date: window.SnapTimezone.getPacificMonthDay()
  };
  
  return await window.DataCacheManager.getData(
    'todayVsPreviousYears',
    params,
    async (params) => {
      // Use web worker for heavy computation
      return new Promise((resolve, reject) => {
        const worker = new Worker('./performance-optimizations/async-data-worker.js');
        
        worker.postMessage({
          type: 'generateTodayVsPreviousYears',
          config: params
        });
        
        worker.onmessage = (e) => {
          const { success, data, error } = e.data;
          worker.terminate();
          
          if (success) {
            resolve(data);
          } else {
            reject(new Error(error));
          }
        };
        
        worker.onerror = (error) => {
          worker.terminate();
          reject(error);
        };
      });
    }
  );
}

async function generateMonthlySalesDataForYearCached(year) {
  const today = window.SnapTimezone.getPacificTime();
  const currentYear = today.getFullYear();
  const monthsToShow = (year === currentYear) ? today.getMonth() + 1 : 12;
  // Zero-data mock mode: short-circuit with zeroed months
  if (window.MockZeroData && window.MockZeroData.isEnabled()) {
    return window.MockZeroData.getZeroMonthlySalesData(year, monthsToShow);
  }
  
  const params = { year, monthsToShow };
  
  return await window.DataCacheManager.getData(
    'monthlySales',
    params,
    async (params) => {
      // Use web worker for heavy computation
      return new Promise((resolve, reject) => {
        const worker = new Worker('./performance-optimizations/async-data-worker.js');
        
        worker.postMessage({
          type: 'generateMonthlySales',
          config: params
        });
        
        worker.onmessage = (e) => {
          const { success, data, error } = e.data;
          worker.terminate();
          
          if (success) {
            resolve(data);
          } else {
            reject(new Error(error));
          }
        };
        
        worker.onerror = (error) => {
          worker.terminate();
          reject(error);
        };
      });
    }
  );
}

// Export for global use
window.generateTodayVsPreviousYearsDataCached = generateTodayVsPreviousYearsDataCached;
window.generateMonthlySalesDataForYearCached = generateMonthlySalesDataForYearCached;
