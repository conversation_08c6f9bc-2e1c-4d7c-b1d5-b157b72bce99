/**
 * Web Worker for Heavy Data Generation
 * Moves expensive data generation off the main thread
 */

// Data generation functions moved to worker
function generateTodayVsPreviousYearsDataAsync(config) {
  const data = [];
  const { startYear, endYear, marketplaceCodes, pacificMonthDay } = config;
  
  // Use requestIdleCallback pattern for chunked processing
  return new Promise((resolve) => {
    let currentYear = startYear;
    
    function processChunk() {
      const chunkSize = 5; // Process 5 years at a time
      const endChunk = Math.min(currentYear + chunkSize, endYear + 1);
      
      for (let year = currentYear; year < endChunk; year++) {
        // Generate data for this year (same logic as original)
        const yearData = generateYearData(year, marketplaceCodes, pacificMonthDay);
        data.push(yearData);
      }
      
      currentYear = endChunk;
      
      if (currentYear <= endYear) {
        // Schedule next chunk
        setTimeout(processChunk, 0);
      } else {
        resolve(data);
      }
    }
    
    processChunk();
  });
}

function generateMonthlySalesDataAsync(year, monthsToShow) {
  return new Promise((resolve) => {
    const monthlyData = [];
    let currentMonth = 0;
    
    function processMonthChunk() {
      const chunkSize = 3; // Process 3 months at a time
      const endChunk = Math.min(currentMonth + chunkSize, monthsToShow);
      
      for (let monthIndex = currentMonth; monthIndex < endChunk; monthIndex++) {
        // Generate month data (same logic as original)
        const monthData = generateMonthData(monthIndex);
        monthlyData.push(monthData);
      }
      
      currentMonth = endChunk;
      
      if (currentMonth < monthsToShow) {
        setTimeout(processMonthChunk, 0);
      } else {
        resolve(monthlyData);
      }
    }
    
    processMonthChunk();
  });
}

// Helper functions
function generateYearData(year, marketplaceCodes, pacificMonthDay) {
  const marketplaces = [];
  let totalSales = 0;
  let totalRoyalties = 0;
  let totalReturns = 0;

  // Create the date for this year using today's month/day
  const yearDate = new Date(year, pacificMonthDay.month, pacificMonthDay.day);

  // Create two-line date format for Today vs Previous Years chart
  const monthAbbreviation = yearDate.toLocaleDateString('en-US', { month: 'short' });
  const day = yearDate.getDate();
  const yearAbbreviation = year.toString().slice(-2); // Get last 2 digits of year

  // First line: Month abbreviation + day (e.g., "Jul 26")
  const monthDay = `${monthAbbreviation} ${day}`;

  // Second line: Year in abbreviated format (e.g., "'25")
  const yearLabel = `'${yearAbbreviation}`;

  // Generate marketplace data for this year with realistic growth trends
  marketplaceCodes.forEach(code => {
    // Implement realistic business growth over 26 years
    let baseSalesMin, baseSalesMax;

    if (year <= 2005) {
      // Early years: 2000-2005
      baseSalesMin = 15;
      baseSalesMax = 50;
    } else if (year <= 2010) {
      // Growth period: 2006-2010
      baseSalesMin = 25;
      baseSalesMax = 70;
    } else if (year <= 2015) {
      // Established business: 2011-2015
      baseSalesMin = 35;
      baseSalesMax = 80;
    } else if (year <= 2020) {
      // Mature business: 2016-2020
      baseSalesMin = 40;
      baseSalesMax = 90;
    } else {
      // Recent years: 2021-2025
      baseSalesMin = 45;
      baseSalesMax = 100;
    }

    // Marketplace-specific multipliers (US is strongest, others vary)
    const marketplaceMultipliers = {
      'US': 1.0,
      'UK': 0.7,
      'DE': 0.8,
      'FR': 0.6,
      'IT': 0.5,
      'ES': 0.4,
      'JP': 0.3
    };

    const multiplier = marketplaceMultipliers[code] || 0.5;
    const adjustedMin = Math.floor(baseSalesMin * multiplier);
    const adjustedMax = Math.floor(baseSalesMax * multiplier);

    // Generate sales with some randomness
    const sales = Math.floor(Math.random() * (adjustedMax - adjustedMin + 1)) + adjustedMin;
    const royalties = Math.floor(sales * 0.15); // 15% royalty rate
    const returns = Math.floor(Math.random() * Math.max(1, sales * 0.05)); // Up to 5% returns

    marketplaces.push({
      code: code,
      sales: sales,
      royalties: royalties,
      returns: returns
    });

    totalSales += sales;
    totalRoyalties += royalties;
    totalReturns += returns;
  });

  // Create backward compatibility arrays
  const values = marketplaces.map(m => m.sales);
  const labels = marketplaces.map(m => m.code);

  return {
    monthDay: monthDay,
    year: yearLabel,
    marketplaces: marketplaces,
    sales: totalSales,
    royalties: totalRoyalties,
    returns: totalReturns,
    values: values,
    labels: labels,
    fullDate: yearDate.toISOString().split('T')[0],
    dateObj: yearDate.toISOString()
  };
}

function generateMonthData(monthIndex) {
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  const allMarketplaces = ["US", "UK", "DE", "FR", "IT", "ES", "JP"];

  // Seasonal patterns: some months are naturally higher/lower
  const seasonalMultipliers = [
    0.8,  // Jan - post-holiday dip
    0.9,  // Feb - slow month
    1.1,  // Mar - spring pickup
    1.2,  // Apr - strong month
    1.0,  // May - average
    0.9,  // Jun - summer slowdown
    0.8,  // Jul - vacation month
    0.9,  // Aug - still slow
    1.1,  // Sep - back to school
    1.3,  // Oct - strong fall
    1.4,  // Nov - pre-holiday surge
    1.2   // Dec - holiday month
  ];

  const seasonalMultiplier = seasonalMultipliers[monthIndex] || 1.0;

  // Randomly decide how many marketplaces have sales (4-7 for more realistic data)
  const numActiveMarketplaces = Math.floor(Math.random() * 4) + 4;

  // Randomly select which marketplaces are active
  const shuffled = [...allMarketplaces].sort(() => 0.5 - Math.random());
  const activeMarketplaces = shuffled.slice(0, numActiveMarketplaces);

  // Generate sales data for each marketplace with realistic distribution and seasonal effects
  const salesData = {};
  let totalSales = 0;
  let totalRoyalties = 0;
  let totalReturns = 0;

  allMarketplaces.forEach(marketplace => {
    if (activeMarketplaces.includes(marketplace)) {
      // Base sales ranges with seasonal adjustment
      let baseSales;
      if (marketplace === 'US') {
        baseSales = Math.floor((Math.random() * 800 + 200) * seasonalMultiplier);
      } else if (['UK', 'DE'].includes(marketplace)) {
        baseSales = Math.floor((Math.random() * 400 + 100) * seasonalMultiplier);
      } else if (['FR', 'IT', 'ES'].includes(marketplace)) {
        baseSales = Math.floor((Math.random() * 200 + 50) * seasonalMultiplier);
      } else {
        baseSales = Math.floor((Math.random() * 100 + 25) * seasonalMultiplier);
      }

      const royalties = Math.floor(baseSales * 0.15);
      const returns = Math.floor(Math.random() * Math.max(1, baseSales * 0.05));

      salesData[marketplace] = {
        sales: baseSales,
        royalties: royalties,
        returns: returns
      };

      totalSales += baseSales;
      totalRoyalties += royalties;
      totalReturns += returns;
    } else {
      salesData[marketplace] = {
        sales: 0,
        royalties: 0,
        returns: 0
      };
    }
  });

  // Create marketplace array for compatibility
  const marketplaces = allMarketplaces.map(code => ({
    code: code,
    sales: salesData[code].sales,
    royalties: salesData[code].royalties,
    returns: salesData[code].returns
  }));

  // Create backward compatibility arrays
  const values = marketplaces.map(m => m.sales);
  const labels = marketplaces.map(m => m.code);

  return {
    monthIndex: monthIndex,
    month: monthNames[monthIndex],
    marketplaces: marketplaces,
    sales: totalSales,
    royalties: totalRoyalties,
    returns: totalReturns,
    values: values,
    labels: labels
  };
}

// Worker message handler
self.onmessage = async function(e) {
  const { type, config } = e.data;
  
  try {
    let result;
    
    switch (type) {
      case 'generateTodayVsPreviousYears':
        result = await generateTodayVsPreviousYearsDataAsync(config);
        break;
      case 'generateMonthlySales':
        result = await generateMonthlySalesDataAsync(config.year, config.monthsToShow);
        break;
      default:
        throw new Error(`Unknown task type: ${type}`);
    }
    
    self.postMessage({ success: true, data: result });
  } catch (error) {
    self.postMessage({ success: false, error: error.message });
  }
};
