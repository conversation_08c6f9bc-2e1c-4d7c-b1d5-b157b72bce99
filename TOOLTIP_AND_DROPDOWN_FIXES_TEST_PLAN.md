# Tooltip Data Preservation & Dropdown Management Fixes Test Plan

## 🎯 Test Objectives
Verify that the two critical issues with show/hide options have been resolved:

1. ✅ **Tooltip Data Preservation**: Tooltips show complete data regardless of show/hide states
2. ✅ **Dropdown Management Integration**: All dropdowns close properly when clicking outside or opening others

## 🧪 Critical Test Scenarios

### Test 1: Tooltip Data Preservation
**Objective**: Verify tooltips always show complete data regardless of show/hide option states

**Steps**:
1. Load dashboard and navigate to any chart with show/hide options
2. Hover over a chart column to see tooltip - note complete data (sales, returns, royalties)
3. Open show/hide options dropdown
4. Uncheck "Show Returns" - verify returns labels disappear visually
5. Hover over same chart column - verify tooltip STILL shows returns data
6. Uncheck "Show Royalties" - verify royalties line disappears visually  
7. Hover over same chart column - verify tooltip STILL shows royalties data
8. Test with comparison mode active - verify both main and comparison tooltips show complete data

**Expected Results**:
- ✅ Visual elements hide/show based on options
- ✅ Tooltips ALWAYS show complete data (sales, returns, royalties)
- ✅ Show/hide functionality is purely cosmetic
- ✅ No data is lost or filtered from tooltips

### Test 2: Monthly Sales Dropdown Management
**Objective**: Verify Monthly Sales dropdown management works correctly

**Steps**:
1. Go to Monthly Sales chart
2. Click show/hide options button - verify dropdown opens
3. Click year dropdown button - verify show/hide dropdown closes and year dropdown opens
4. Click compare button - verify year dropdown closes and compare dropdown opens
5. Click show/hide options button - verify compare dropdown closes and show/hide dropdown opens
6. Click anywhere outside dropdowns - verify all dropdowns close
7. Open any dropdown, then click on chart area - verify dropdown closes

**Expected Results**:
- ✅ Only one dropdown open at a time
- ✅ Opening one dropdown closes others
- ✅ Clicking outside closes all dropdowns
- ✅ Clicking on chart area closes dropdowns

### Test 3: Global Dropdown Management (Other Charts)
**Objective**: Verify dropdown management for Last Week's Sales, Today vs Previous Years, and Yearly Sales

**Steps**:
1. Go to Last Week's Sales chart
2. Click show/hide options button - verify dropdown opens
3. Go to Today vs Previous Years chart
4. Click show/hide options button - verify Last Week's dropdown closes and Today vs Previous opens
5. Go to Yearly Sales chart  
6. Click show/hide options button - verify Today vs Previous dropdown closes and Yearly opens
7. Click anywhere outside dropdowns - verify all show/hide dropdowns close
8. Test with marketplace dropdown - verify show/hide dropdowns close when marketplace opens

**Expected Results**:
- ✅ Only one show/hide dropdown open at a time across all charts
- ✅ Opening one show/hide dropdown closes others
- ✅ Clicking outside closes all show/hide dropdowns
- ✅ Marketplace dropdown closes show/hide dropdowns

### Test 4: Cross-Chart Dropdown Interaction
**Objective**: Verify dropdowns interact correctly across different chart types

**Steps**:
1. Open Monthly Sales show/hide dropdown
2. Click Last Week's Sales show/hide button - verify Monthly dropdown closes
3. Open Yearly Sales show/hide dropdown
4. Click Monthly Sales year dropdown - verify Yearly show/hide dropdown closes
5. Open Today vs Previous Years show/hide dropdown
6. Click marketplace dropdown - verify Today vs Previous dropdown closes
7. Test various combinations of dropdown interactions

**Expected Results**:
- ✅ Show/hide dropdowns close when other chart dropdowns open
- ✅ Chart-specific dropdowns (year, compare) close show/hide dropdowns
- ✅ Marketplace dropdown closes all other dropdowns
- ✅ Consistent behavior across all chart types

### Test 5: State Persistence with Tooltip Preservation
**Objective**: Verify state persistence works with tooltip data preservation

**Steps**:
1. Go to Monthly Sales chart
2. Uncheck "Show Returns" and "Show Royalties"
3. Verify visual elements are hidden
4. Hover over columns - verify tooltips show complete data
5. Change year selection (e.g., 2025 to 2024)
6. Verify show/hide options remain unchecked
7. Verify visual elements remain hidden in new year data
8. Hover over columns - verify tooltips show complete data for new year
9. Switch to Lifetime view and back - verify state and tooltips preserved

**Expected Results**:
- ✅ Show/hide state persists during year changes
- ✅ Visual elements remain hidden/shown correctly
- ✅ Tooltips always show complete data for any year/view
- ✅ No data loss during state transitions

## 🔍 Visual Verification Checklist

### When Show/Hide Options Are Active:
- [ ] Visual elements hide/show as expected (returns labels, royalties lines)
- [ ] Tooltips show complete data regardless of visual state
- [ ] Sales values positioned correctly when returns hidden
- [ ] Chart rendering is smooth and responsive

### When Dropdowns Are Opened:
- [ ] Only one dropdown visible at a time
- [ ] Proper dropdown animations and styling
- [ ] Clicking outside closes all dropdowns
- [ ] No visual glitches or overlapping dropdowns

### During State Changes:
- [ ] Show/hide options persist during year changes
- [ ] Tooltips remain accurate during all transitions
- [ ] No console errors during dropdown interactions
- [ ] Smooth user experience with no delays

## 🚀 Performance Verification
- [ ] Tooltip display is immediate and accurate
- [ ] Dropdown opening/closing is smooth
- [ ] No memory leaks during repeated interactions
- [ ] Chart re-rendering is efficient (visual only)
- [ ] No unnecessary data processing for tooltips

## ✅ Success Criteria
All tests must pass with the expected results. The implementation should provide:

1. **Complete Tooltip Data**: Tooltips always show sales, returns, and royalties regardless of show/hide states
2. **Proper Dropdown Management**: All dropdowns close when clicking outside or opening others
3. **Visual-Only Show/Hide**: Show/hide functionality affects only visual rendering, not data
4. **State Persistence**: Show/hide options maintain state during year changes
5. **Seamless Integration**: Works perfectly with existing dropdown systems
6. **Performance**: Fast, responsive, and error-free operation

## 🎯 Key Behavioral Changes

### Before Fixes:
- ❌ Tooltips showed incomplete data when options were hidden
- ❌ Dropdowns didn't close properly when clicking outside
- ❌ Show/hide dropdowns interfered with existing dropdown management

### After Fixes:
- ✅ Tooltips always show complete data (purely visual show/hide)
- ✅ All dropdowns close properly with consistent behavior
- ✅ Show/hide dropdowns integrated seamlessly with existing systems
