/**
 * DateChangeManager Verification Script
 * Run this in the browser console to verify the DateChangeManager is working properly
 */

(function() {
  'use strict';

  console.log('🧪 Starting DateChangeManager verification...');

  // Test 1: Check if DateChangeManager is available
  function testManagerAvailability() {
    console.log('\n📋 Test 1: Manager Availability');
    
    if (window.DateChangeManager) {
      console.log('✅ DateChangeManager is available');
      console.log('✅ Type:', typeof window.DateChangeManager);
      
      // Check required methods
      const requiredMethods = ['startMonitoring', 'stopMonitoring', 'registerCallback', 'getStatus', 'performHealthCheck', 'getDiagnostics'];
      requiredMethods.forEach(method => {
        if (typeof window.DateChangeManager[method] === 'function') {
          console.log(`✅ Method ${method} is available`);
        } else {
          console.log(`❌ Method ${method} is missing`);
        }
      });
    } else {
      console.log('❌ DateChangeManager is not available');
      return false;
    }
    
    return true;
  }

  // Test 2: Check current status
  function testCurrentStatus() {
    console.log('\n📋 Test 2: Current Status');
    
    try {
      const status = window.DateChangeManager.getStatus();
      console.log('📊 Current Status:', status);
      
      if (status.isActive) {
        console.log('✅ DateChangeManager is active');
      } else {
        console.log('⚠️ DateChangeManager is not active');
      }
      
      console.log(`📅 Last known date: ${status.lastKnownDate}`);
      console.log(`🔄 Callback count: ${status.callbackCount}`);
      console.log(`⏰ Near midnight: ${status.isNearMidnight}`);
      console.log(`👁️ Tab visible: ${status.isTabVisible}`);
      
    } catch (error) {
      console.log('❌ Error getting status:', error);
    }
  }

  // Test 3: Get comprehensive diagnostics
  function testDiagnostics() {
    console.log('\n📋 Test 3: Comprehensive Diagnostics');
    
    try {
      if (window.getDateChangeManagerDiagnostics) {
        const diagnostics = window.getDateChangeManagerDiagnostics();
        console.log('🔍 Diagnostics:', diagnostics);
        
        // Check key indicators
        if (diagnostics.isActive) {
          console.log('✅ System is active');
        } else {
          console.log('❌ System is not active');
        }
        
        if (diagnostics.unifiedTimerManagerAvailable) {
          console.log('✅ UnifiedTimerManager is available');
          if (diagnostics.unifiedTimerManagerActive) {
            console.log('✅ UnifiedTimerManager is active');
          } else {
            console.log('⚠️ UnifiedTimerManager is not active');
          }
        } else {
          console.log('⚠️ UnifiedTimerManager is not available');
        }
        
        console.log(`📅 Current Pacific Date: ${diagnostics.currentPacificDate}`);
        console.log(`🕐 Current Pacific Time: ${diagnostics.currentPacificTime}`);
        console.log(`🔄 Registered Callbacks: ${diagnostics.registeredCallbacks.length}`);
        
      } else {
        console.log('❌ Diagnostics function not available');
      }
    } catch (error) {
      console.log('❌ Error getting diagnostics:', error);
    }
  }

  // Test 4: Perform health check
  function testHealthCheck() {
    console.log('\n📋 Test 4: Health Check');
    
    try {
      if (window.checkDateChangeManagerHealth) {
        console.log('🏥 Performing health check...');
        window.checkDateChangeManagerHealth();
        console.log('✅ Health check completed - check console output above');
      } else {
        console.log('❌ Health check function not available');
      }
    } catch (error) {
      console.log('❌ Error during health check:', error);
    }
  }

  // Test 5: Force a date check
  function testForceCheck() {
    console.log('\n📋 Test 5: Force Date Check');
    
    try {
      if (window.forceDateChangeCheck) {
        console.log('🔍 Forcing date change check...');
        window.forceDateChangeCheck();
        console.log('✅ Force check completed');
      } else {
        console.log('❌ Force check function not available');
      }
    } catch (error) {
      console.log('❌ Error during force check:', error);
    }
  }

  // Test 6: Check timezone functionality
  function testTimezone() {
    console.log('\n📋 Test 6: Timezone Functionality');
    
    try {
      if (window.SnapTimezone) {
        console.log('✅ SnapTimezone is available');
        
        const pacificTime = window.SnapTimezone.getPacificTime();
        const pacificDate = window.SnapTimezone.getPacificDate();
        const dateString = window.SnapTimezone.getCurrentPacificDateString();
        
        console.log(`🕐 Pacific Time: ${pacificTime.toString()}`);
        console.log(`📅 Pacific Date: ${pacificDate.toString()}`);
        console.log(`📝 Date String: ${dateString}`);
        
      } else {
        console.log('❌ SnapTimezone is not available');
      }
    } catch (error) {
      console.log('❌ Error testing timezone:', error);
    }
  }

  // Run all tests
  function runAllTests() {
    console.log('🚀 Running DateChangeManager verification tests...');
    
    const managerAvailable = testManagerAvailability();
    if (!managerAvailable) {
      console.log('\n❌ DateChangeManager not available - stopping tests');
      return;
    }
    
    testCurrentStatus();
    testDiagnostics();
    testHealthCheck();
    testForceCheck();
    testTimezone();
    
    console.log('\n✅ Verification tests completed!');
    console.log('\n💡 To monitor date changes:');
    console.log('   - Check console logs around midnight Pacific Time');
    console.log('   - Use window.getDateChangeManagerDiagnostics() for status');
    console.log('   - Use window.checkDateChangeManagerHealth() for health checks');
  }

  // Expose functions globally for manual testing
  window.verifyDateChangeManager = runAllTests;
  window.testDateChangeManagerStatus = testCurrentStatus;
  window.testDateChangeManagerDiagnostics = testDiagnostics;

  // Auto-run tests
  runAllTests();

})();

console.log('\n🔧 DateChangeManager verification script loaded!');
console.log('💡 Run window.verifyDateChangeManager() to run tests again');
