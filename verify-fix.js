// Verification script for yearly chart full-width fix
// This script can be run in the browser console to verify the fix

console.log('🔍 Verifying Yearly Chart Full-Width Fix...');

// Check if the yearly chart container exists
const yearlyContainer = document.querySelector('#yearly-sales-chart-container');
if (!yearlyContainer) {
    console.log('❌ Yearly chart container not found');
} else {
    console.log('✅ Yearly chart container found');
    
    // Check if chart instance exists
    if (yearlyContainer.snapChart) {
        console.log('✅ Yearly chart instance found');
        
        const chart = yearlyContainer.snapChart;
        console.log('📊 Chart type:', chart.type);
        console.log('📊 Chart options:', chart.options);
        
        // Check if fullWidthDistribution is enabled
        if (chart.options.fullWidthDistribution) {
            console.log('✅ fullWidthDistribution is enabled');
        } else {
            console.log('❌ fullWidthDistribution is NOT enabled');
        }
        
        // Check the chart's SVG elements to see column distribution
        const chartSvg = yearlyContainer.querySelector('svg');
        if (chartSvg) {
            const columns = chartSvg.querySelectorAll('.snap-chart-column-group');
            console.log(`📊 Found ${columns.length} columns`);
            
            if (columns.length >= 2) {
                // Get positions of first and last columns
                const firstColumn = columns[0];
                const lastColumn = columns[columns.length - 1];
                
                const firstX = parseFloat(firstColumn.getAttribute('transform')?.match(/translate\(([^,]+)/)?.[1] || 0);
                const lastX = parseFloat(lastColumn.getAttribute('transform')?.match(/translate\(([^,]+)/)?.[1] || 0);
                
                console.log(`📊 First column X: ${firstX}px`);
                console.log(`📊 Last column X: ${lastX}px`);
                console.log(`📊 Column spread: ${lastX - firstX}px`);
                
                // Check container width
                const containerWidth = yearlyContainer.offsetWidth;
                console.log(`📊 Container width: ${containerWidth}px`);
                
                // Calculate if columns are using full width (rough check)
                const spreadRatio = (lastX - firstX) / containerWidth;
                console.log(`📊 Spread ratio: ${(spreadRatio * 100).toFixed(1)}%`);
                
                if (spreadRatio > 0.7) {
                    console.log('✅ Columns appear to be using full width');
                } else {
                    console.log('❌ Columns appear to be left-aligned');
                }
            }
        }
    } else {
        console.log('❌ Yearly chart instance not found');
    }
}

// Compare with Today vs Previous Years chart
const todayVsContainer = document.querySelector('#today-vs-previous-years-chart-container');
if (todayVsContainer && todayVsContainer.snapChart) {
    console.log('📊 Today vs Previous Years chart for comparison:');
    console.log('📊 isTodayVsPreviousYearsChart:', todayVsContainer.snapChart.options.isTodayVsPreviousYearsChart);
    console.log('📊 fullWidthDistribution:', todayVsContainer.snapChart.options.fullWidthDistribution);
}

console.log('🔍 Verification complete. Check the logs above for results.');
