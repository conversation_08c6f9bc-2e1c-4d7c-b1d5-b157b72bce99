/**
 * Test Script for Compare Mode Grid/Axes Rendering Fix
 * 
 * This script tests the fix for the intermittent bug where grid lines and vertical axes
 * don't render properly in charts with compare mode enabled.
 * 
 * The fix includes:
 * 1. Enhanced validateBaseGridAndAxes function with multiple retries for compare mode
 * 2. Forced DOM flush after drawing grid/axes in compare mode
 * 3. Longer retry delays for complex compare mode rendering
 */

// Test configuration
const TEST_CONFIG = {
  iterations: 20,           // Number of test iterations
  delayBetweenTests: 100,   // Delay between tests in ms
  compareMode: true,        // Test with compare mode enabled
  logResults: true          // Log detailed results
};

// Test results tracking
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  details: []
};

/**
 * Validate that a chart has proper grid lines and axes
 */
function validateChartRendering(chartContainer, testId) {
  const svg = chartContainer.querySelector('svg');
  if (!svg) {
    return { valid: false, reason: 'No SVG element found' };
  }

  const gridLines = svg.querySelectorAll('.snap-chart-grid-line');
  const axisLabels = svg.querySelectorAll('.snap-chart-axis-label');

  const gridCount = gridLines.length;
  const axisCount = axisLabels.length;

  const isValid = gridCount >= 6 && axisCount >= 6;

  return {
    valid: isValid,
    gridCount,
    axisCount,
    reason: isValid ? 'Valid' : `Insufficient elements: ${gridCount} grid lines, ${axisCount} axis labels`
  };
}

/**
 * Create a test chart with compare mode
 */
function createTestChart(containerId) {
  // Sample data for testing
  const testData = [
    { month: 'JAN', year: '25', sales: 1200, royalties: 480, values: [400, 300, 500], labels: ['US', 'UK', 'DE'] },
    { month: 'FEB', year: '25', sales: 800, royalties: 320, values: [300, 200, 300], labels: ['US', 'UK', 'DE'] },
    { month: 'MAR', year: '25', sales: 1500, royalties: 600, values: [500, 400, 600], labels: ['US', 'UK', 'DE'] }
  ];

  const compareData = [
    { month: 'JAN', year: '24', sales: 1000, royalties: 400, values: [350, 250, 400], labels: ['US', 'UK', 'DE'] },
    { month: 'FEB', year: '24', sales: 900, royalties: 360, values: [300, 300, 300], labels: ['US', 'UK', 'DE'] },
    { month: 'MAR', year: '24', sales: 1100, royalties: 440, values: [400, 300, 400], labels: ['US', 'UK', 'DE'] }
  ];

  return new SnapChart({
    container: containerId,
    type: 'stacked-column',
    data: testData,
    options: {
      title: `Test Chart - Compare Mode`,
      subtitle: 'Testing grid/axes rendering in compare mode',
      compareMode: TEST_CONFIG.compareMode,
      compareData: compareData,
      animate: false, // Disable animations for faster testing
      responsive: true
    }
  });
}

/**
 * Run a single test iteration
 */
async function runSingleTest(testId) {
  return new Promise((resolve) => {
    // Create container
    const container = document.createElement('div');
    container.id = `test-container-${testId}`;
    container.style.cssText = `
      width: 800px;
      height: 400px;
      position: absolute;
      top: -1000px;
      left: -1000px;
      visibility: hidden;
    `;
    document.body.appendChild(container);

    try {
      // Create chart
      const chart = createTestChart(container.id);

      // Wait for rendering to complete
      setTimeout(() => {
        const result = validateChartRendering(container, testId);
        
        testResults.total++;
        if (result.valid) {
          testResults.passed++;
        } else {
          testResults.failed++;
        }

        testResults.details.push({
          testId,
          ...result,
          timestamp: new Date().toISOString()
        });

        if (TEST_CONFIG.logResults) {
          console.log(`Test ${testId}: ${result.valid ? 'PASS' : 'FAIL'} - ${result.reason}`);
        }

        // Cleanup
        document.body.removeChild(container);
        resolve(result);
      }, 200); // Wait 200ms for rendering

    } catch (error) {
      testResults.total++;
      testResults.failed++;
      testResults.details.push({
        testId,
        valid: false,
        reason: `Error: ${error.message}`,
        timestamp: new Date().toISOString()
      });

      console.error(`Test ${testId}: ERROR - ${error.message}`);
      document.body.removeChild(container);
      resolve({ valid: false, reason: `Error: ${error.message}` });
    }
  });
}

/**
 * Run the complete test suite
 */
async function runTestSuite() {
  console.log('🧪 Starting Compare Mode Grid/Axes Rendering Test Suite');
  console.log(`Configuration: ${TEST_CONFIG.iterations} iterations, compare mode: ${TEST_CONFIG.compareMode}`);
  console.log('─'.repeat(60));

  const startTime = Date.now();

  for (let i = 1; i <= TEST_CONFIG.iterations; i++) {
    await runSingleTest(i);
    
    // Add delay between tests
    if (i < TEST_CONFIG.iterations) {
      await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.delayBetweenTests));
    }
  }

  const endTime = Date.now();
  const duration = endTime - startTime;

  // Print results
  console.log('─'.repeat(60));
  console.log('📊 Test Results Summary:');
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} (${((testResults.passed / testResults.total) * 100).toFixed(1)}%)`);
  console.log(`Failed: ${testResults.failed} (${((testResults.failed / testResults.total) * 100).toFixed(1)}%)`);
  console.log(`Duration: ${duration}ms`);
  console.log(`Average per test: ${(duration / testResults.total).toFixed(1)}ms`);

  // Show failure details if any
  const failures = testResults.details.filter(d => !d.valid);
  if (failures.length > 0) {
    console.log('\n❌ Failed Tests:');
    failures.forEach(failure => {
      console.log(`  Test ${failure.testId}: ${failure.reason}`);
    });
  } else {
    console.log('\n✅ All tests passed! The compare mode fix is working correctly.');
  }

  return testResults;
}

/**
 * Initialize and run tests when DOM is ready
 */
function initializeTests() {
  if (typeof SnapChart === 'undefined') {
    console.error('❌ SnapChart library not found. Please ensure snap-charts.js is loaded.');
    return;
  }

  console.log('✅ SnapChart library detected');
  runTestSuite().catch(error => {
    console.error('❌ Test suite failed:', error);
  });
}

// Auto-run tests if this script is loaded in a browser environment
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeTests);
  } else {
    initializeTests();
  }
}

// Export for Node.js environments
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runTestSuite,
    validateChartRendering,
    TEST_CONFIG,
    testResults
  };
}
