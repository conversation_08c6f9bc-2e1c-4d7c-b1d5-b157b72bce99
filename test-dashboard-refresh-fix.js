/**
 * Test Script for Dashboard Refresh Infinite Loop Fix
 * 
 * This script tests the fix for the infinite loop issue where clicking the refresh button
 * multiple times while a refresh is already in progress would cause overlapping refresh operations.
 * 
 * The fix includes:
 * 1. Global refresh state management (isDashboardRefreshing flag)
 * 2. Button state management (disabled state and visual feedback)
 * 3. Duplicate click protection with timing information
 * 4. Proper cleanup in finally block to ensure state is always reset
 */

// Test configuration
const TEST_CONFIG = {
  rapidClickCount: 10,      // Number of rapid clicks to simulate
  clickInterval: 50,        // Interval between clicks in ms
  testDuration: 5000,       // Total test duration in ms
  logResults: true          // Log detailed results
};

// Test results tracking
let testResults = {
  totalClicks: 0,
  acceptedClicks: 0,
  rejectedClicks: 0,
  refreshOperations: 0,
  errors: [],
  timings: []
};

/**
 * Simulate rapid clicking on the dashboard refresh button
 */
function simulateRapidClicks() {
  return new Promise((resolve) => {
    const refreshBtn = document.querySelector('.dashboard-refresh-btn');
    if (!refreshBtn) {
      console.error('❌ Dashboard refresh button not found');
      resolve({ success: false, reason: 'Button not found' });
      return;
    }

    console.log(`🧪 Starting rapid click test: ${TEST_CONFIG.rapidClickCount} clicks at ${TEST_CONFIG.clickInterval}ms intervals`);
    
    let clickCount = 0;
    const startTime = Date.now();
    
    // Monitor console logs to track refresh operations
    const originalConsoleLog = console.log;
    const originalConsoleWarn = console.warn;
    
    console.log = function(...args) {
      const message = args.join(' ');
      if (message.includes('[Refresh Debug] Starting dashboard refresh')) {
        testResults.refreshOperations++;
        testResults.acceptedClicks++;
      }
      originalConsoleLog.apply(console, args);
    };
    
    console.warn = function(...args) {
      const message = args.join(' ');
      if (message.includes('[Refresh Protection] Dashboard refresh already in progress')) {
        testResults.rejectedClicks++;
      }
      originalConsoleWarn.apply(console, args);
    };
    
    const clickInterval = setInterval(() => {
      if (clickCount >= TEST_CONFIG.rapidClickCount) {
        clearInterval(clickInterval);
        
        // Restore original console methods
        console.log = originalConsoleLog;
        console.warn = originalConsoleWarn;
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        testResults.totalClicks = clickCount;
        testResults.timings.push({
          startTime,
          endTime,
          duration,
          clicksPerSecond: (clickCount / duration) * 1000
        });
        
        console.log(`🧪 Rapid click test completed: ${clickCount} clicks in ${duration}ms`);
        resolve({ success: true, duration, clickCount });
        return;
      }
      
      // Simulate click
      const clickTime = Date.now();
      refreshBtn.click();
      clickCount++;
      testResults.totalClicks = clickCount;
      
      if (TEST_CONFIG.logResults) {
        console.log(`🖱️ Click ${clickCount} at ${clickTime - startTime}ms`);
      }
      
    }, TEST_CONFIG.clickInterval);
  });
}

/**
 * Test button state management
 */
function testButtonStateManagement() {
  const refreshBtn = document.querySelector('.dashboard-refresh-btn');
  if (!refreshBtn) {
    return { success: false, reason: 'Button not found' };
  }
  
  console.log('🧪 Testing button state management...');
  
  // Check initial state
  const initialState = {
    disabled: refreshBtn.disabled,
    hasRefreshingClass: refreshBtn.classList.contains('refreshing'),
    cursor: getComputedStyle(refreshBtn).cursor
  };
  
  console.log('Initial button state:', initialState);
  
  // Simulate click and check state changes
  refreshBtn.click();
  
  // Check state immediately after click
  setTimeout(() => {
    const activeState = {
      disabled: refreshBtn.disabled,
      hasRefreshingClass: refreshBtn.classList.contains('refreshing'),
      cursor: getComputedStyle(refreshBtn).cursor
    };
    
    console.log('Active refresh state:', activeState);
    
    // Check if button is properly disabled during refresh
    const isProperlyDisabled = activeState.disabled && activeState.hasRefreshingClass;
    console.log(`Button properly disabled during refresh: ${isProperlyDisabled ? '✅' : '❌'}`);
    
  }, 100);
  
  return { success: true, initialState };
}

/**
 * Test refresh state persistence
 */
function testRefreshStatePersistence() {
  console.log('🧪 Testing refresh state persistence...');
  
  // Check if global refresh state variables exist
  const hasGlobalState = typeof window.isDashboardRefreshing !== 'undefined';
  console.log(`Global refresh state available: ${hasGlobalState ? '✅' : '❌'}`);
  
  if (hasGlobalState) {
    console.log(`Current refresh state: ${window.isDashboardRefreshing}`);
    console.log(`Refresh start time: ${window.refreshStartTime}`);
  }
  
  return { success: hasGlobalState };
}

/**
 * Run comprehensive test suite
 */
async function runTestSuite() {
  console.log('🧪 Starting Dashboard Refresh Infinite Loop Fix Test Suite');
  console.log('─'.repeat(60));
  
  const startTime = Date.now();
  
  // Test 1: Button state management
  console.log('\n📋 Test 1: Button State Management');
  const buttonTest = testButtonStateManagement();
  console.log(`Result: ${buttonTest.success ? '✅ PASS' : '❌ FAIL'}`);
  
  // Test 2: Refresh state persistence
  console.log('\n📋 Test 2: Refresh State Persistence');
  const stateTest = testRefreshStatePersistence();
  console.log(`Result: ${stateTest.success ? '✅ PASS' : '❌ FAIL'}`);
  
  // Test 3: Rapid click protection
  console.log('\n📋 Test 3: Rapid Click Protection');
  const rapidClickTest = await simulateRapidClicks();
  console.log(`Result: ${rapidClickTest.success ? '✅ PASS' : '❌ FAIL'}`);
  
  // Wait for any ongoing refresh to complete
  console.log('\n⏳ Waiting for refresh operations to complete...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  const endTime = Date.now();
  const totalDuration = endTime - startTime;
  
  // Print comprehensive results
  console.log('\n' + '─'.repeat(60));
  console.log('📊 Test Results Summary:');
  console.log(`Total Test Duration: ${totalDuration}ms`);
  console.log(`Total Clicks Simulated: ${testResults.totalClicks}`);
  console.log(`Accepted Clicks: ${testResults.acceptedClicks}`);
  console.log(`Rejected Clicks: ${testResults.rejectedClicks}`);
  console.log(`Actual Refresh Operations: ${testResults.refreshOperations}`);
  
  // Calculate protection effectiveness
  const protectionRate = testResults.totalClicks > 0 ? 
    ((testResults.rejectedClicks / testResults.totalClicks) * 100).toFixed(1) : 0;
  console.log(`Protection Rate: ${protectionRate}% (${testResults.rejectedClicks}/${testResults.totalClicks} clicks blocked)`);
  
  // Determine overall result
  const isFixWorking = testResults.refreshOperations <= 1 && testResults.rejectedClicks > 0;
  console.log(`\n${isFixWorking ? '✅' : '❌'} Overall Result: ${isFixWorking ? 'FIX IS WORKING' : 'FIX NEEDS ATTENTION'}`);
  
  if (isFixWorking) {
    console.log('🎉 The infinite loop fix is working correctly!');
    console.log('   - Multiple clicks are being blocked');
    console.log('   - Only one refresh operation is running');
    console.log('   - Button state is properly managed');
  } else {
    console.log('⚠️ The fix may need attention:');
    if (testResults.refreshOperations > 1) {
      console.log(`   - Multiple refresh operations detected: ${testResults.refreshOperations}`);
    }
    if (testResults.rejectedClicks === 0) {
      console.log('   - No clicks were blocked (protection not working)');
    }
  }
  
  return testResults;
}

/**
 * Initialize and run tests when DOM is ready
 */
function initializeTests() {
  console.log('🔍 Checking for dashboard refresh button...');
  
  const refreshBtn = document.querySelector('.dashboard-refresh-btn');
  if (!refreshBtn) {
    console.error('❌ Dashboard refresh button not found. Make sure you are on the dashboard page.');
    return;
  }
  
  console.log('✅ Dashboard refresh button found');
  
  // Run tests after a short delay to ensure everything is loaded
  setTimeout(() => {
    runTestSuite().catch(error => {
      console.error('❌ Test suite failed:', error);
    });
  }, 1000);
}

// Auto-run tests if this script is loaded in a browser environment
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeTests);
  } else {
    initializeTests();
  }
}

// Export for Node.js environments
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runTestSuite,
    simulateRapidClicks,
    testButtonStateManagement,
    testRefreshStatePersistence,
    TEST_CONFIG,
    testResults
  };
}
