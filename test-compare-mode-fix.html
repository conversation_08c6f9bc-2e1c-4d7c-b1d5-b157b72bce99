<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compare Mode Grid/Axes Fix Test</title>
    
    <!-- Load existing project CSS -->
    <link rel="stylesheet" href="snapapp.css">
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-family: 'Amazon Ember', Arial, sans-serif;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 32px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .test-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-accent);
            margin: 0 0 8px 0;
        }
        
        .test-subtitle {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }
        
        .controls-section {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-bottom: 32px;
            flex-wrap: wrap;
        }
        
        .control-btn {
            padding: 8px 16px;
            border: 1.5px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Amazon Ember', sans-serif;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--theme-transition);
        }
        
        .control-btn:hover {
            background: var(--btn-hover);
            border-color: var(--action-btn-bg);
        }
        
        .control-btn.active {
            background: var(--action-btn-bg);
            color: var(--action-btn-text);
            border-color: var(--action-btn-bg);
        }
        
        .chart-container {
            width: 100%;
            max-width: 1064px;
            margin: 0 auto;
            background: var(--bg-primary);
            border: 1.5px solid var(--border-color);
            border-radius: 14px;
            padding: 24px;
        }
        
        .test-info {
            background: var(--bg-primary);
            border: 1.5px solid var(--border-color);
            border-radius: 14px;
            padding: 24px;
            margin-top: 32px;
        }
        
        .test-info h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 700;
            color: var(--text-accent);
        }
        
        .test-info p {
            margin: 0 0 12px 0;
            font-size: 14px;
            line-height: 1.5;
            color: var(--text-primary);
        }
        
        .test-results {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-pass { background-color: #4CAF50; }
        .status-fail { background-color: #F44336; }
        .status-pending { background-color: #FF9800; }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-header">
            <h1 class="test-title">Compare Mode Grid/Axes Fix Test</h1>
            <p class="test-subtitle">Testing the fix for intermittent grid lines and axes rendering issues in compare mode</p>
        </div>
        
        <!-- Controls -->
        <div class="controls-section">
            <button class="control-btn active" id="lightTheme">Light Theme</button>
            <button class="control-btn" id="darkTheme">Dark Theme</button>
            <button class="control-btn" id="toggleCompare">Enable Compare</button>
            <button class="control-btn" id="runTests">Run Automated Tests</button>
            <button class="control-btn" id="stressTest">Stress Test (50x)</button>
        </div>
        
        <!-- Chart Container -->
        <div class="chart-container" id="chartContainer">
            <h2 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 700; color: var(--text-accent);">
                <span class="status-indicator status-pending" id="statusIndicator"></span>
                Test Chart - Compare Mode
            </h2>
        </div>
        
        <!-- Test Information -->
        <div class="test-info">
            <h3>🔧 Fix Details</h3>
            <p>This test demonstrates the fix for the intermittent bug where grid lines and vertical axes don't render in charts with compare mode enabled.</p>
            
            <p><strong>The Problem:</strong> In compare mode, the complex rendering process (main columns + comparison columns + royalties lines) sometimes completed before the grid lines and axes were properly committed to the DOM, causing them to appear missing.</p>
            
            <p><strong>The Solution:</strong></p>
            <ul style="margin: 12px 0; padding-left: 20px;">
                <li>Enhanced <code>validateBaseGridAndAxes</code> function with multiple retry attempts for compare mode</li>
                <li>Longer retry delays (16ms vs 8ms) for complex compare mode rendering</li>
                <li>Forced DOM flush after drawing grid/axes in compare mode using <code>getBoundingClientRect()</code></li>
                <li>Up to 4 retry attempts for compare mode vs 2 for normal mode</li>
            </ul>
            
            <h3>🧪 Test Results</h3>
            <div class="test-results" id="testResults">
                <div>Click "Run Automated Tests" to validate the fix...</div>
            </div>
        </div>
    </div>

    <!-- Load Chart JavaScript -->
    <script src="components/charts/dummy-data.js"></script>
    <script src="components/charts/data-loader.js"></script>
    <script src="components/charts/snap-charts.js"></script>
    <script src="test-compare-mode-fix.js"></script>
    
    <script>
        let chart = null;
        let compareMode = false;
        
        // Sample data for testing
        const testData = [
            { month: 'JAN', year: '25', sales: 1200, royalties: 480, values: [400, 300, 500], labels: ['US', 'UK', 'DE'] },
            { month: 'FEB', year: '25', sales: 800, royalties: 320, values: [300, 200, 300], labels: ['US', 'UK', 'DE'] },
            { month: 'MAR', year: '25', sales: 1500, royalties: 600, values: [500, 400, 600], labels: ['US', 'UK', 'DE'] },
            { month: 'APR', year: '25', sales: 1100, royalties: 440, values: [350, 350, 400], labels: ['US', 'UK', 'DE'] }
        ];

        const compareData = [
            { month: 'JAN', year: '24', sales: 1000, royalties: 400, values: [350, 250, 400], labels: ['US', 'UK', 'DE'] },
            { month: 'FEB', year: '24', sales: 900, royalties: 360, values: [300, 300, 300], labels: ['US', 'UK', 'DE'] },
            { month: 'MAR', year: '24', sales: 1100, royalties: 440, values: [400, 300, 400], labels: ['US', 'UK', 'DE'] },
            { month: 'APR', year: '24', sales: 950, royalties: 380, values: [300, 300, 350], labels: ['US', 'UK', 'DE'] }
        ];
        
        function createChart() {
            const container = document.getElementById('chartContainer');
            
            // Clear existing chart
            if (chart) {
                container.innerHTML = '<h2 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 700; color: var(--text-accent);"><span class="status-indicator status-pending" id="statusIndicator"></span>Test Chart - Compare Mode</h2>';
            }
            
            chart = new SnapChart({
                container: '#chartContainer',
                type: 'stacked-column',
                data: testData,
                options: {
                    title: 'Test Chart - Compare Mode',
                    subtitle: compareMode ? 'Compare mode enabled - testing grid/axes rendering' : 'Compare mode disabled',
                    compareMode: compareMode,
                    compareData: compareData,
                    animate: false,
                    responsive: true
                }
            });
            
            // Validate rendering after a short delay
            setTimeout(() => {
                validateCurrentChart();
            }, 300);
        }
        
        function validateCurrentChart() {
            const container = document.getElementById('chartContainer');
            const result = validateChartRendering(container, 'manual');
            const indicator = document.getElementById('statusIndicator');
            
            if (result.valid) {
                indicator.className = 'status-indicator status-pass';
                console.log('✅ Chart validation passed:', result);
            } else {
                indicator.className = 'status-indicator status-fail';
                console.log('❌ Chart validation failed:', result);
            }
        }
        
        // Theme controls
        document.getElementById('lightTheme').addEventListener('click', function() {
            document.documentElement.setAttribute('data-theme', 'light');
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
        
        document.getElementById('darkTheme').addEventListener('click', function() {
            document.documentElement.setAttribute('data-theme', 'dark');
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
        
        // Compare mode toggle
        document.getElementById('toggleCompare').addEventListener('click', function() {
            compareMode = !compareMode;
            this.textContent = compareMode ? 'Disable Compare' : 'Enable Compare';
            this.classList.toggle('active', compareMode);
            createChart();
        });
        
        // Test controls
        document.getElementById('runTests').addEventListener('click', function() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div>Running automated tests...</div>';
            
            // Override test config for this demo
            TEST_CONFIG.iterations = 10;
            TEST_CONFIG.compareMode = true;
            TEST_CONFIG.logResults = true;
            
            runTestSuite().then(results => {
                const passRate = ((results.passed / results.total) * 100).toFixed(1);
                resultsDiv.innerHTML = `
                    <div><strong>Test Results:</strong></div>
                    <div>Total: ${results.total}, Passed: ${results.passed}, Failed: ${results.failed}</div>
                    <div>Pass Rate: ${passRate}%</div>
                    <div style="margin-top: 10px;">${passRate >= 95 ? '✅ Fix is working correctly!' : '❌ Issues detected'}</div>
                `;
            });
        });
        
        document.getElementById('stressTest').addEventListener('click', function() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div>Running stress test (50 iterations)...</div>';
            
            TEST_CONFIG.iterations = 50;
            TEST_CONFIG.compareMode = true;
            TEST_CONFIG.logResults = false;
            
            runTestSuite().then(results => {
                const passRate = ((results.passed / results.total) * 100).toFixed(1);
                resultsDiv.innerHTML = `
                    <div><strong>Stress Test Results:</strong></div>
                    <div>Total: ${results.total}, Passed: ${results.passed}, Failed: ${results.failed}</div>
                    <div>Pass Rate: ${passRate}%</div>
                    <div style="margin-top: 10px;">${passRate >= 95 ? '✅ Fix is robust under stress!' : '❌ Issues under stress'}</div>
                `;
            });
        });
        
        // Initialize
        document.documentElement.setAttribute('data-theme', 'light');
        createChart();
    </script>
</body>
</html>
