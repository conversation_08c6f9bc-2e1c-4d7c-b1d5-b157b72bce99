// Ads Insights Component
// This component provides advertising insights and analytics

// Component HTML structure
const adsInsightsHTML = `
<div class="ads-insights-container">
  <div class="page-header">
    <h1 class="page-title">Ads Insights</h1>
    <p class="page-description">Comprehensive advertising analytics and performance insights</p>
  </div>
  
  <div class="content-area">
    <div class="coming-soon-card">
      <div class="coming-soon-content">
        <img src="./assets/snap-ads-ic.svg" alt="Ads Insights" class="coming-soon-icon">
        <h2>Ads Insights</h2>
        <p>Advanced advertising analytics and performance tracking coming soon!</p>
        <div class="features-preview">
          <ul>
            <li>📊 Ad performance metrics</li>
            <li>💰 ACOS tracking and optimization</li>
            <li>📈 Campaign analytics</li>
            <li>🎯 Audience insights</li>
            <li>📋 Detailed reporting</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

// Component CSS styles
const adsInsightsCSS = `
.ads-insights-container {
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.content-area {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.coming-soon-card {
  background: var(--bg-primary);
  border: 1.5px solid var(--border-color);
  border-radius: 12px;
  padding: 48px;
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.coming-soon-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.coming-soon-icon {
  width: 64px;
  height: 64px;
  opacity: 0.7;
}

.coming-soon-card h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.coming-soon-card p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

.features-preview {
  margin-top: 16px;
}

.features-preview ul {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.features-preview li {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  padding-left: 8px;
}

.features-preview li:last-child {
  margin-bottom: 0;
}

/* Dark mode support */
[data-theme="dark"] .coming-soon-card {
  background: var(--bg-secondary);
}

/* Responsive design */
@media (max-width: 768px) {
  .ads-insights-container {
    padding: 16px;
  }
  
  .coming-soon-card {
    padding: 32px 24px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .coming-soon-card h2 {
    font-size: 20px;
  }
}
`;

// Initialize component
function initAdsInsights() {
  console.log('🎯 Initializing Ads Insights component');
  
  // Inject CSS if not already present
  if (!document.querySelector('#ads-insights-styles')) {
    const style = document.createElement('style');
    style.id = 'ads-insights-styles';
    style.textContent = adsInsightsCSS;
    document.head.appendChild(style);
  }
  
  // Get main content area and inject HTML
  const mainContent = document.querySelector('.main-content');
  if (mainContent) {
    mainContent.innerHTML = adsInsightsHTML;
  }
  
  console.log('✅ Ads Insights component initialized');
}

// Export component data
window.adsInsights = {
  render: initAdsInsights,
  html: adsInsightsHTML,
  css: adsInsightsCSS
};

// Auto-initialize if this script is loaded directly
if (typeof window !== 'undefined') {
  // Component is ready for initialization
  console.log('📦 Ads Insights component loaded');
}
