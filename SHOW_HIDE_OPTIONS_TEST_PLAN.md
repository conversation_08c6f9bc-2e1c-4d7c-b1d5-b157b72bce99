# Show/Hide Options Test Plan - Complete Fixes

## 🎯 Test Objectives
Verify that all six issues with the show/hide options implementation have been resolved:

1. ✅ Checkbox styling matches existing dropdowns
2. ✅ Royalties line completely hidden (not flattened)
3. ✅ Returns labels completely hidden (not showing "0")
4. ✅ Comparison columns fully supported
5. ✅ Sales value positioning corrected when returns hidden
6. ✅ Monthly Sales state persistence during year changes

## 🧪 Test Scenarios

### Test 1: Basic Show/Hide Functionality
**Objective**: Verify basic show/hide options work correctly

**Steps**:
1. Load dashboard
2. Locate show/hide options buttons in all four chart cards
3. Click show/hide options button to open dropdown
4. Verify checkbox styling matches existing compare dropdowns
5. Uncheck "Show Returns" - verify returns labels disappear completely
6. Uncheck "Show Royalties" - verify royalties line disappears completely
7. Re-check both options - verify elements reappear

**Expected Results**:
- ✅ Checkboxes have proper opacity (0.2 for unchecked in light theme)
- ✅ Returns labels completely disappear (no "0" values shown)
- ✅ Royalties line completely disappears (not flattened to bottom)
- ✅ Elements reappear correctly when re-enabled

### Test 2: Sales Value Positioning
**Objective**: Verify sales values move down when returns are hidden

**Steps**:
1. Open any chart with show/hide options
2. Note the position of sales values above columns
3. Uncheck "Show Returns"
4. Observe the new position of sales values
5. Re-check "Show Returns"
6. Verify sales values return to original position

**Expected Results**:
- ✅ When returns hidden: Sales values move down to fill the gap
- ✅ When returns shown: Sales values return to original position
- ✅ Consistent spacing maintained across all chart types
- ✅ Both main and comparison columns behave the same way

### Test 3: Comparison Columns Support
**Objective**: Verify show/hide options work with comparison data

**Steps**:
1. Go to Monthly Sales or Last Week's Sales
2. Enable compare mode (click compare button, select option)
3. Verify comparison columns appear
4. Open show/hide options dropdown
5. Uncheck "Show Returns" - verify returns disappear from both main and comparison columns
6. Uncheck "Show Royalties" - verify royalties disappear from both main and comparison columns
7. Re-check options - verify elements reappear in both column types

**Expected Results**:
- ✅ Returns labels hidden in both main and comparison columns
- ✅ Royalties elements hidden in both main and comparison columns
- ✅ Sales value positioning correct in both column types
- ✅ Show/hide options work seamlessly with compare mode

### Test 4: Monthly Sales State Persistence
**Objective**: Verify show/hide options maintain state during year changes

**Steps**:
1. Go to Monthly Sales chart
2. Open show/hide options dropdown
3. Uncheck "Show Returns" and "Show Royalties"
4. Verify elements are hidden
5. Change year selection (e.g., from 2025 to 2024)
6. Verify show/hide options remain unchecked
7. Verify returns and royalties remain hidden in new year data
8. Change to "Lifetime" view
9. Verify show/hide options state is preserved
10. Change back to a specific year
11. Verify state is still preserved

**Expected Results**:
- ✅ Show/hide checkboxes remain unchecked after year change
- ✅ Returns and royalties remain hidden in new year data
- ✅ State preserved when switching between year and lifetime views
- ✅ Behavior matches compare functionality persistence

### Test 5: Integration with Existing Features
**Objective**: Verify show/hide options work with other dashboard features

**Steps**:
1. Test with marketplace filtering active
2. Test with compare mode active
3. Test with different chart types (stacked vs scrollable)
4. Test responsive behavior on mobile
5. Test dark theme compatibility
6. Test with zero values and missing data

**Expected Results**:
- ✅ Works correctly with marketplace filtering
- ✅ Works correctly with compare mode
- ✅ Works correctly with all chart types
- ✅ Responsive behavior maintained
- ✅ Dark theme styling correct
- ✅ Handles edge cases properly

### Test 6: Session Persistence
**Objective**: Verify session storage works correctly

**Steps**:
1. Set show/hide options to specific states across all charts
2. Navigate between different dashboard sections
3. Return to charts - verify states are maintained
4. Refresh browser tab
5. Verify states reset to defaults (both checked)

**Expected Results**:
- ✅ States maintained during navigation within session
- ✅ States reset to defaults on page refresh
- ✅ Each chart maintains independent state
- ✅ No conflicts between chart states

## 🔍 Visual Verification Checklist

### When "Show Returns" is UNCHECKED:
- [ ] No returns labels visible anywhere (main or comparison columns)
- [ ] Sales values positioned closer to column tops (gap filled)
- [ ] No "(0)" or "(-X)" text visible
- [ ] Checkbox appears with reduced opacity (light theme) or normal opacity (dark theme)

### When "Show Royalties" is UNCHECKED:
- [ ] No royalties line visible
- [ ] No royalties dots visible
- [ ] No comparison royalties dots visible
- [ ] Checkbox appears with reduced opacity (light theme) or normal opacity (dark theme)

### When BOTH are UNCHECKED:
- [ ] Only sales columns and values visible
- [ ] Sales values positioned optimally (no excessive gaps)
- [ ] Clean, uncluttered chart appearance
- [ ] Both checkboxes show unchecked state

## 🚀 Performance Verification
- [ ] Chart updates happen immediately (no delays)
- [ ] No console errors during show/hide operations
- [ ] Smooth transitions and animations
- [ ] No memory leaks during repeated operations
- [ ] Responsive performance on mobile devices

## ✅ Success Criteria
All tests must pass with the expected results. The implementation should provide:
1. **Visual Consistency**: Matches existing dropdown styling
2. **Complete Element Hiding**: No flattened lines or "0" labels
3. **Proper Positioning**: Sales values positioned correctly when returns hidden
4. **State Persistence**: Monthly Sales maintains state during year changes
5. **Full Integration**: Works seamlessly with all existing features
6. **Performance**: Fast, responsive, and error-free operation
