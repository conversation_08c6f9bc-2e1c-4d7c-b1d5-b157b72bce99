<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Refresh Infinite Loop Fix Test</title>
    
    <!-- Load existing project CSS -->
    <link rel="stylesheet" href="snapapp.css">
    
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-family: 'Amazon Ember', Arial, sans-serif;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 32px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .test-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-accent);
            margin: 0 0 8px 0;
        }
        
        .test-subtitle {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }
        
        .demo-section {
            background: var(--bg-primary);
            border: 1.5px solid var(--border-color);
            border-radius: 14px;
            padding: 24px;
            text-align: center;
        }
        
        .demo-section h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 700;
            color: var(--text-accent);
        }
        
        .demo-section p {
            margin: 0 0 20px 0;
            font-size: 14px;
            color: var(--text-primary);
        }
        
        .refresh-demo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }
        
        .demo-instructions {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            text-align: left;
        }
        
        .demo-instructions h4 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-accent);
        }
        
        .demo-instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .demo-instructions li {
            margin: 8px 0;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .test-controls {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: 1.5px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Amazon Ember', sans-serif;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--theme-transition);
        }
        
        .test-btn:hover {
            background: var(--btn-hover);
            border-color: var(--action-btn-bg);
        }
        
        .test-btn.primary {
            background: var(--action-btn-bg);
            color: var(--action-btn-text);
            border-color: var(--action-btn-bg);
        }
        
        .test-results {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            text-align: left;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-pass { background-color: #4CAF50; }
        .status-fail { background-color: #F44336; }
        .status-pending { background-color: #FF9800; }
        
        .refresh-status {
            margin: 16px 0;
            padding: 12px;
            border-radius: 6px;
            font-weight: 500;
        }
        
        .refresh-status.idle {
            background: #E8F5E8;
            color: #2E7D32;
            border: 1px solid #C8E6C9;
        }
        
        .refresh-status.active {
            background: #FFF3E0;
            color: #F57C00;
            border: 1px solid #FFCC02;
        }
        
        [data-theme="dark"] .refresh-status.idle {
            background: #1B2E1B;
            color: #81C784;
            border-color: #388E3C;
        }
        
        [data-theme="dark"] .refresh-status.active {
            background: #2E1B0A;
            color: #FFB74D;
            border-color: #F57C00;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-header">
            <h1 class="test-title">Dashboard Refresh Infinite Loop Fix</h1>
            <p class="test-subtitle">Testing the fix for multiple simultaneous refresh operations</p>
        </div>
        
        <!-- Demo Section -->
        <div class="demo-section">
            <h3>🔄 Interactive Demo</h3>
            <p>Try clicking the refresh button multiple times rapidly to test the infinite loop protection.</p>
            
            <div class="refresh-demo">
                <!-- Dashboard Refresh Button Mount -->
                <div id="dashboard-refresh-mount"></div>
                
                <div style="margin-left: 20px;">
                    <div class="refresh-status idle" id="refreshStatus">
                        <span class="status-indicator status-pass"></span>
                        Refresh State: Idle
                    </div>
                </div>
            </div>
            
            <div class="demo-instructions">
                <h4>🧪 Manual Testing Instructions:</h4>
                <ol>
                    <li><strong>Single Click Test:</strong> Click the refresh button once and observe the spinning animation</li>
                    <li><strong>Rapid Click Test:</strong> Click the refresh button multiple times rapidly while it's refreshing</li>
                    <li><strong>Expected Behavior:</strong> Only one refresh operation should run, additional clicks should be ignored</li>
                    <li><strong>Visual Feedback:</strong> Button should be disabled and show spinning animation during refresh</li>
                    <li><strong>Console Logs:</strong> Open DevTools Console to see protection messages</li>
                </ol>
            </div>
            
            <div class="test-controls">
                <button class="test-btn primary" id="runAutomatedTest">Run Automated Test</button>
                <button class="test-btn" id="rapidClickTest">Simulate 10 Rapid Clicks</button>
                <button class="test-btn" id="clearResults">Clear Results</button>
            </div>
            
            <div class="test-results" id="testResults">
                <div>Click "Run Automated Test" to validate the infinite loop fix...</div>
            </div>
        </div>
        
        <!-- Fix Details -->
        <div class="demo-section">
            <h3>🔧 Fix Implementation Details</h3>
            <div style="text-align: left;">
                <p><strong>Problem:</strong> Clicking the refresh button multiple times while a refresh was already in progress would start multiple overlapping refresh operations, leading to an infinite loop.</p>
                
                <p><strong>Solution Components:</strong></p>
                <ul style="margin: 12px 0; padding-left: 20px;">
                    <li><strong>Global State Management:</strong> <code>isDashboardRefreshing</code> flag prevents overlapping operations</li>
                    <li><strong>Button State Control:</strong> Button is disabled and shows visual feedback during refresh</li>
                    <li><strong>Duplicate Click Protection:</strong> Additional clicks are logged and ignored with timing information</li>
                    <li><strong>Proper Cleanup:</strong> State is always reset in finally block, even if errors occur</li>
                    <li><strong>Visual Feedback:</strong> Spinning animation and disabled state provide clear user feedback</li>
                </ul>
                
                <p><strong>Key Code Changes:</strong></p>
                <ul style="margin: 12px 0; padding-left: 20px;">
                    <li>Added <code>isDashboardRefreshing</code> and <code>refreshStartTime</code> global variables</li>
                    <li>Enhanced <code>setupDashboardRefresh()</code> with state management</li>
                    <li>Added safeguard in <code>refreshDashboardCards()</code> function</li>
                    <li>Added CSS for refreshing state with spin animation</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Load Dashboard JavaScript -->
    <script src="components/dashboard/dashboard.js"></script>
    <script src="test-dashboard-refresh-fix.js"></script>
    
    <script>
        let refreshStatusElement = document.getElementById('refreshStatus');
        let testResultsElement = document.getElementById('testResults');
        
        // Initialize dashboard refresh button
        function initializeDashboard() {
            // Set up the dashboard refresh functionality
            if (typeof setupDashboardRefresh === 'function') {
                setupDashboardRefresh();
                console.log('✅ Dashboard refresh functionality initialized');
            } else {
                console.error('❌ setupDashboardRefresh function not found');
            }
            
            // Monitor refresh state changes
            monitorRefreshState();
        }
        
        // Monitor refresh state and update UI
        function monitorRefreshState() {
            setInterval(() => {
                const isRefreshing = window.isDashboardRefreshing || false;
                const refreshBtn = document.querySelector('.dashboard-refresh-btn');
                
                if (isRefreshing) {
                    refreshStatusElement.className = 'refresh-status active';
                    refreshStatusElement.innerHTML = '<span class="status-indicator status-pending"></span>Refresh State: Active (Refreshing...)';
                } else {
                    refreshStatusElement.className = 'refresh-status idle';
                    refreshStatusElement.innerHTML = '<span class="status-indicator status-pass"></span>Refresh State: Idle';
                }
                
                // Update button state indicator
                if (refreshBtn) {
                    const isDisabled = refreshBtn.disabled;
                    const hasSpinning = refreshBtn.classList.contains('refreshing');
                    
                    if (isDisabled || hasSpinning) {
                        refreshStatusElement.innerHTML += ' (Button Disabled)';
                    }
                }
            }, 100);
        }
        
        // Test controls
        document.getElementById('runAutomatedTest').addEventListener('click', function() {
            testResultsElement.innerHTML = '<div>Running automated test suite...</div>';
            
            if (typeof runTestSuite === 'function') {
                runTestSuite().then(results => {
                    const passRate = results.totalClicks > 0 ? 
                        ((results.rejectedClicks / results.totalClicks) * 100).toFixed(1) : 0;
                    
                    testResultsElement.innerHTML = `
                        <div><strong>Automated Test Results:</strong></div>
                        <div>Total Clicks: ${results.totalClicks}</div>
                        <div>Accepted Clicks: ${results.acceptedClicks}</div>
                        <div>Rejected Clicks: ${results.rejectedClicks}</div>
                        <div>Refresh Operations: ${results.refreshOperations}</div>
                        <div>Protection Rate: ${passRate}%</div>
                        <div style="margin-top: 10px; font-weight: bold;">
                            ${results.refreshOperations <= 1 && results.rejectedClicks > 0 ? 
                                '✅ Fix is working correctly!' : 
                                '❌ Fix needs attention'}
                        </div>
                    `;
                }).catch(error => {
                    testResultsElement.innerHTML = `<div style="color: #F44336;">❌ Test failed: ${error.message}</div>`;
                });
            } else {
                testResultsElement.innerHTML = '<div style="color: #F44336;">❌ Test functions not available</div>';
            }
        });
        
        document.getElementById('rapidClickTest').addEventListener('click', function() {
            testResultsElement.innerHTML = '<div>Simulating 10 rapid clicks...</div>';
            
            if (typeof simulateRapidClicks === 'function') {
                simulateRapidClicks().then(result => {
                    testResultsElement.innerHTML = `
                        <div><strong>Rapid Click Test Results:</strong></div>
                        <div>Clicks Simulated: ${result.clickCount}</div>
                        <div>Duration: ${result.duration}ms</div>
                        <div>Result: ${result.success ? '✅ Completed' : '❌ Failed'}</div>
                        <div style="margin-top: 10px;">Check console for detailed protection logs</div>
                    `;
                });
            }
        });
        
        document.getElementById('clearResults').addEventListener('click', function() {
            testResultsElement.innerHTML = '<div>Results cleared. Ready for new tests...</div>';
        });
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Set light theme
            document.documentElement.setAttribute('data-theme', 'light');
            
            // Initialize dashboard
            setTimeout(initializeDashboard, 500);
        });
    </script>
</body>
</html>
